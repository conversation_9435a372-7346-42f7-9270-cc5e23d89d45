import axios from '@/utils/axios'
import _api from '@/api/index'
import { platform } from '@/utils/setBaseUrl'
import constants from '@/utils/constants.js'

const lesson = {
  state: {
    planRecording: false, // 计划录制中
    needGenerateResource: false,
    regenerateLesson: null, // 重新生成课程的数据
    planCenterType: 'PS', // center 类型，默认是 PS
    lessonInfoMappedFramework: null, // 在课程信息中选择要映射的框架
    addCurriculumNeedGenerateResource: false, // 新增课程时，判断是否生成资源
    firstVisit: false, // 是否第一次触发周计划引导
    hasIEPChild: true, // 是否有 IEP 学生，一开始默认是存在的，目的是为了用户第一次进来是可以看得到对应的空的表格知道后续会生成对应的数据，而不是什么都没有，用于 personalizePlan 中将属性传递给差异化教学组件
    hasELDChild: true, // 是否有 ELD 学生，一开始默认是存在的，目的是为了用户第一次进来是可以看得到对应的空的表格知道后续会生成对应的数据，而不是什么都没有， 用于 personalizePlan 中将属性传递给差异化教学组件
    adaptUDLAndCLROpen: undefined, // 是否开启差异化教学
    hideIEPOpen: undefined, // 是否隐藏 IEP
    isOpenAddChildrenTip: false, // 是否显示添加小孩提示
    // 周计划模板可以选择的框架
    frameworks: [
      {
        'frameworkId': 'F9E1415E-CFC0-4BC8-9698-7249A303584D',
        'frameworkName': 'California Preschool/Transitional Kindergarten Learning Foundations',
        'frameworkUrl': '',
        'defaultGrade': 'TK (4-5)',
        'drdp': false
      },
      {
         'frameworkId': 'E163164F-BDCE-E411-AF66-02C72B94B99B',
         'frameworkName': 'DRDP2015-INFANT-TODDLER Comprehensive View',
         'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015-IT-Comprehensive-View-20200124_ADA.pdf',
         'defaultGrade': 'TK (4-5)',
         'drdp': true
      },
      {
        'frameworkId': '32DF6B7B-A5A0-4B73-AF23-193175BC537C',
        'frameworkName': 'DRDP2015-INFANT-TODDLER Essential View',
        'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015-IT-Essential-View-20190628_ADA.pdf',
        'defaultGrade': 'TK (4-5)',
        'drdp': true
      },
      {
         'frameworkId': '********-BDCE-E411-AF66-02C72B94B99B',
         'frameworkName': 'DRDP2015-PRESCHOOL Comprehensive view',
         'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015_PSC_Comprehensive_View_Combined-20200219_ADA.pdf',
         'defaultGrade': 'TK (4-5)',
         'drdp': true
      },
      {
        'frameworkId': 'E028CC1B-000D-461C-AC85-D86A38BF8B0F',
        'frameworkName': 'DRDP2015-Kindergarten Essential View',
        'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015K_Essential20221116.pdf',
        'defaultGrade': 'K (5-6)',
        'drdp': true
      },
      {
        'frameworkId': 'DA5D2340-6D56-41B9-A0B6-5F3962043E8E',
        'frameworkName': 'DRDP2015-Kindergarten Fundamental View',
        'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015K_Fundamental_20221116.pdf',
        'defaultGrade': 'K (5-6)',
        'drdp': true
      },
      {
        'frameworkId': '3FCFD736-4D4E-431C-A0AC-FCE0C2622B3F',
        'frameworkName': 'DRDP2015-PRESCHOOL Essential View',
        'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015_Combined_PSE_20190624_ADA.pdf',
        'defaultGrade': 'TK (4-5)',
        'drdp': true
      },
      {
        'frameworkId': '49DA6264-E437-E611-AB42-06BC895D03FD',
        'frameworkName': 'DRDP2015-Preschool Fundamental View',
        'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015_Combined_PSF_20200219_ADA.pdf',
        'defaultGrade': 'TK (4-5)',
        'drdp': true
      },
      {
         'frameworkId': '3F7D9A2A-32EE-E411-AF66-02C72B94B99B',
         'frameworkName': 'DRDP2011-SA (complete version)',
         'frameworkUrl': 'https://desiredresults.us/sites/default/files/docs/forms/DRDP-SA(2011) Complete100311.pdf',
         'defaultGrade': 'Grade 3',
         'drdp': true
      },
      {
         'frameworkId': 'A21BC800-2FF5-E411-AF66-02C72B94B99B',
         'frameworkName': 'DRDP2015-Kindergarten',
         'frameworkUrl': 'https://www.desiredresults.us/sites/default/files/docs/forms/DRDP2015K_Final_20221116.pdf',
         'defaultGrade': 'K (5-6)',
         'drdp': true
      },
      {
        'frameworkId': 'C5AEAF21-B44B-4B87-B603-231D768DEA71',
        'frameworkName': 'IL CCSS-K Rubrics (First Trimester)',
        'frameworkUrl': 'https://s3.amazonaws.com/com.learning-genie.cdn/pdf/IL+CCSS-K+Rubrics+(First+Trimester).pdf',
        'defaultGrade': 'K (5-6)',
        'drdp': false
      },
      {
        'frameworkId': 'E83EB44A-BD11-4003-B32A-79B17065A408',
        'frameworkName': 'IL CCSS-K Rubrics',
        'defaultGrade': 'K (5-6)',
        'frameworkUrl': '',
        'drdp': false
      },
      {
        'frameworkId': '31DA5BF9-BD21-46BC-86ED-00F2493D415D',
        'frameworkName': 'First Grade Power Standars',
        'frameworkUrl': '',
        'defaultGrade': 'Grade 1',
        'drdp': false
      },
      {
        'frameworkId': 'C32820B1-2600-4ED9-AF2C-94A010F8B5AE',
        'frameworkName': 'Missouri Early Learning Standards (MELS)',
        'frameworkUrl': '',
        'defaultGrade': 'Infant (0-1)',
        'drdp': false
      },
      {
        'frameworkId': 'D47854DF-F042-9DDF-0985-EBE90008733F',
        'frameworkName': 'OUSD TK Standards',
        'frameworkUrl': '',
        'defaultGrade': 'TK (4-5)',
        'drdp': false
      }
    ],
    showAIGroup: false, // 是否显示 AI 组
    enableMixedAgeGroup: false, // 是否启用混龄组
    planItem: null, // 周计划活动项数据
    planInterpretationGuide: false, // 是否显示周计划解读引导
    planGenerateLessonGuide: false, // 是否显示周计划 AI 生成课程引导
    singleAdaptGuide: false, // 是否显示单个适配引导
    templateType: '', // 模板类型
    showMappedTip: false, // 是否显示映射提示
    unitAdapted: false, // 是否单元已改编
    needUpdateLessonTemplate: false, // 是否更新课程模板
    showClrSource: false, // 是否显示 CLR 资源
    showMappedTypicalBehaviors: false, // 是否显示映射的典型行为
    lessonDownloadFileLangCode: '', // 课程详情下载文件语言码
    lessonDownloadFileOriginalLangCode: '', // 课程详情下载文件原始语言码
    showImpStepSourceMap: {}, // 下载时是否显示实施步骤资源
    agencyLearnerProfile: {  // 机构默认校训
      "id": null,
      "learnerProfile": null,
      "ageGroupRubrics": null,
      "editRecords": null,
      "canEditOrDelete": null,
      "newAgeGroupRubrics": null,
      "mixedAgeGroup": true,
      "generateExpectations": true
    },
    mapFrameworkData: [], // 框架映射数据
    lessonTemplates: [], // 课程模板数据
    lessonResourceStatus: { // 课程资源状态
      slidesResourceUrl: '',
      templateResourceUrls: [],
      hasQuizResource: false
    },
    // 课程模块设置
    lessonModuleSetting: {
      teachingTips: true,
      typicalBehaviors: true,
      teacherSlides: true,
      eduprotocols: true,
      mixedAgeDifferentiations: true,
      clr0toTk: true,
      clrKto12: true,
      readingPassage: true,
      udl: true,
      atHomeActivities: true,
      formativeAssessment: true
    }
  },
  mutations: {
    // 设置模块设置
    SET_LESSON_MODULE_SETTINGS (state, settings) {
      state.lessonModuleSetting = settings
    },
    SET_ENABLE_MIXED_AGE_GROUP (state, boole) {
      state.enableMixedAgeGroup = boole
    },
    SET_AGENCY_LEARNER_PROFILE (state, profile) {
      state.agencyLearnerProfile = profile
    },
    // 设置课程详情下载文件源语言码
    SET_LESSON_DOWNLOAD_FILE_ORIGINAL_LANG_CODE (state, langCode) {
      state.lessonDownloadFileOriginalLangCode = langCode
    },
    // 设置课程详情的模版类型
    SET_TEMPLATE_TYPE (state, type) {
      state.templateType = type
    },
    // 设置是否显示映射提示
    SET_SHOW_MAPPED_TIP (state, boole) {
      state.showMappedTip = boole
    },
    // 设置是否单元已改编
    SET_UNIT_ADAPTED (state, boole) {
      state.unitAdapted = boole
    },
    // 设置是否更新课程模板
    SET_NEED_UPDATE_LESSON_TEMPLATE (state, boole) {
      state.needUpdateLessonTemplate = boole
    },
    // 设置课程详情下载文件语言码
    SET_LESSON_DOWNLOAD_FILE_LANG_CODE (state, langCode) {
      state.lessonDownloadFileLangCode = langCode
    },
    SET_GENERATERESOURCE (state, boole) {
      state.needGenerateResource = boole
    },
    SET_PLANCENTERTYPE (state, type) {
      state.planCenterType = type
    },
    SET_HASIEPCHILD (state, boole) {
        state.hasIEPChild = boole
    },
    SET_LESSONINFOMAPPEDFRAMEWORK (state, framework) {
      state.lessonInfoMappedFramework = framework
    },
    SET_HASELDCHILD (state, boole) {
        state.hasELDChild = boole
    },
    SET_ADDCURRICULUMNEEDGENERATERESOURCE (state, boole) {
      state.addCurriculumNeedGenerateResource = boole
    },
    SET_PLANRECORDING (state, boole) {
      state.planRecording = boole
    },
    SET_FIRSTVISIT (state, boole) {
      state.firstVisit = boole
    },
    SET_ADAPTUDLANDCLROPEN (state, boole) {
        state.adaptUDLAndCLROpen = boole
    },
    SET_HIDEIEP (state, boole) {
        state.hideIEPOpen = boole
    },
    SET_IS_OPEN_ADD_CHILDREN_TIP (state, boole) {
        state.isOpenAddChildrenTip = boole
    },
    SET_PLAN_ITEM (state, item) {
      state.planItem = item
    },
    SET_PLAN_INTERPRETATION_GUIDE (state, boole) {
      state.planInterpretationGuide = boole
    },
    SET_PLAN_GENERATE_LESSON_GUIDE (state, boole) {
      state.planGenerateLessonGuide = boole
    },
    SET_SINGLE_ADAPT_GUIDE (state, boole) {
      state.singleAdaptGuide = boole
    },
    SET_SHOW_CLR_SOURCE (state, boole) {
      state.showClrSource = boole
    },
    SET_SHOW_MAPPED_TYPICAL_BEHAVIORS (state, boole) {
      state.showMappedTypicalBehaviors = boole
    },
    SET_SHOW_IMP_STEP_SOURCE_MAP (state, map) {
      state.showImpStepSourceMap = map
    },
    SET_MAP_FRAMEWORK_DATA (state, data) {
      state.mapFrameworkData = data
    },
    SET_REGENERATE_LESSON(state, data) {
      state.regenerateLesson = data
    },
    // 设置是否有幻灯片资源
    SET_HAS_SLIDES_RESOURCE (state, hasSlides) {
      state.lessonResourceStatus.slidesResourceUrl = hasSlides
    },
    // 设置是否有模板资源
    SET_HAS_TEMPLATE_RESOURCE (state, hasTemplate) {
      state.lessonResourceStatus.templateResourceUrls = hasTemplate
    },
    // 设置是否有问答资源
    SET_HAS_QUIZ_RESOURCE (state, hasQuiz) {
      state.lessonResourceStatus.hasQuizResource = hasQuiz
    }
  },
  actions: {
    // 获取用户模块设置
    async getLessonModuleSettings ({ commit }) {
      try {
        const settings = await axios.get(_api.urls().getLessonModuleSetting)
        commit('SET_LESSON_MODULE_SETTINGS', settings)
      } catch (error) {
        console.error('Failed to fetch module settings:', error)
      }
    },
    // 更新课程模块设置
    async saveLessonModuleSettings ({ commit }, settings) {
      commit('SET_LESSON_MODULE_SETTINGS', settings)
      try {
        await axios.post(_api.urls().saveLessonModuleSetting, settings)
      } catch (error) {
        console.error('Failed to fetch module settings:', error)
      }
    },
    setGenerateResource ({ commit }, boole) {
      commit('SET_GENERATERESOURCE', boole)
    },
    setPlanCenterTpye ({ commit }, type) {
      commit('SET_PLANCENTERTYPE', type)
    },
    setHasIEPChild ({ commit }, boole) {
        commit('SET_HASIEPCHILD', boole)
    },
    setHasELDChild ({ commit }, boole) {
        commit('SET_HASELDCHILD', boole)
    },
    setLessonInfoMappedFramework ({ commit }, framework) {
      commit('SET_LESSONINFOMAPPEDFRAMEWORK', framework)
    },
    setAddCurriculumNeedGenerateResource ({ commit }, boole) {
      commit('SET_ADDCURRICULUMNEEDGENERATERESOURCE', boole)
    },
    setPlanRecording ({ commit }, boole) {
      commit('SET_PLANRECORDING', boole)
    },
    setFirstVisit ({ commit }, boole) {
      commit('SET_FIRSTVISIT', boole)
    },
    setAdaptUDLAndCLROpen ({ commit }, boole) {
        commit('SET_ADAPTUDLANDCLROPEN', boole)
    },
    setHideIEP ({ commit }, boole) {
        commit('SET_HIDEIEP', boole)
    },
    setIsOpenAddChildrenTip ({ commit }, boole) {
        commit('SET_IS_OPEN_ADD_CHILDREN_TIP', boole)
    },
    setPlanItem ({ commit }, item) {
      commit('SET_PLAN_ITEM', item)
    },
    setPlanInterpretationGuide ({ commit }, boole) {
      commit('SET_PLAN_INTERPRETATION_GUIDE', boole)
    },
    setPlanGenerateLessonGuide ({ commit }, boole) {
      commit('SET_PLAN_GENERATE_LESSON_GUIDE', boole)
    },
    setSingleAdaptGuide ({ commit }, boole) {
      commit('SET_SINGLE_ADAPT_GUIDE', boole)
    },
    setShowClrSource ({ commit }, boole) {
      commit('SET_SHOW_CLR_SOURCE', boole)
    },
    setShowMappedTip ({ commit }, boole) {
      commit('SET_SHOW_MAPPED_TIP', boole)
    },
    setTemplateType ({ commit }, type) {
      commit('SET_TEMPLATE_TYPE', type)
    },
    setUnitAdapted ({ commit }, boole) {
      commit('SET_UNIT_ADAPTED', boole)
    },
    setNeedUpdateLessonTemplate ({ commit }, boole) {
      commit('SET_NEED_UPDATE_LESSON_TEMPLATE', boole)
    },
    setShowMappedTypicalBehaviors ({ commit }, boole) {
      commit('SET_SHOW_MAPPED_TYPICAL_BEHAVIORS', boole)
    },
    // 控制下载时是否显示实施步骤资源
    setShowImpStepSourceMap ({ commit }, map) {
      commit('SET_SHOW_IMP_STEP_SOURCE_MAP', map)
    },
    // 获取当前机构默认校训
    async getAgencyLearnerProfile({ commit }) {
      // MC 平台不需要校训
      if (platform === 'MAGIC-CURRICULUM') {
        return
      }
      try {
        const res = await axios.get(_api.urls().getAgencyLearnerProfile)
        commit('SET_AGENCY_LEARNER_PROFILE', res || null)
      } catch (error) {
        console.log(error)
      }
    },
    getMapFrameworkData ({ commit, state }) {
      return new Promise((resolve, reject) => {
        // 如果已存在，直接返回
        if (state.mapFrameworkData && state.mapFrameworkData.length > 0) {
          resolve(state.mapFrameworkData)
          return
        }
        axios.get(_api.urls().getFrameworkMeasureMap, { params: { frameworkId: constants.pscFrameworkId } })
        .then(res => {
          commit('SET_MAP_FRAMEWORK_DATA', res.measures || [])
          resolve(res.measures || [])
        })
        .catch(err => {
          reject(err)
        })
      })
    },
    setRegenerateLesson({ commit }, data) {
      commit('SET_REGENERATE_LESSON', data)
    },
    // 设置是否有幻灯片资源
    setHasSlidesResource ({ commit }, hasSlides) {
      commit('SET_HAS_SLIDES_RESOURCE', hasSlides)
    },
    // 设置是否有模板资源
    setHasTemplateResource ({ commit }, hasTemplate) {
      commit('SET_HAS_TEMPLATE_RESOURCE', hasTemplate)
    },
    // 设置是否有问答资源
    setHasQuizResource ({ commit }, hasQuiz) {
      commit('SET_HAS_QUIZ_RESOURCE', hasQuiz)
    }
  }
}

export default lesson
