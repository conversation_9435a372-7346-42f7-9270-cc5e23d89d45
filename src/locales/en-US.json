{"saveSfy": "Saved successfully!", "giveAtry": "Give it a try!", "descfont": "Unit Planner Overview", "overview": "Overview", "more": "Learn More", "materials": "Materials Overview", "trajectory": "Unit Trajectory", "concepts": "Concepts", "unitaddress1": "Guiding Questions", "doSameTitle": "Do you want to try adding a video book of the same title?", "activity": "Activity", "activitys": "Activitys", "assessment1": "Assessment Standards/Measures", "assessment2": "Assessment Standards/Measures List", "uptSfy": "Updated successfully", "attachFile": "Attach File", "attachFile1": "Printables", "readmore1": "Read More", "readmore2": "Read Less", "agenAdmin": "Agency Admin", "siteAdmin": "Site Admin", "gteAdm": "<PERSON><PERSON>", "f": "Warning Children's names do not match either due to spelling difference or quantity mismatch.", "agency": "Agency", "add": "Add", "addNew": "Add New", "addCenter": "Add New Site", "lastName": "Last Name", "apply": "Apply", "items": "items", "back": "Back", "help": "Help", "search": "Search", "cancel": "Cancel", "loading": "Loading...", "uploading": "Uploading...", "home": "Home", "center": "Site", "centers": "Sites", "close": "Close", "signOut": "Sign Out", "complete": "complete", "setting": "Setting", "admSet": "<PERSON><PERSON>s", "confirmation": "Confirmation", "cfm": "Confirmation", "confirm": "Confirm", "tip": "Tip", "count": "count", "content": "Content", "password": "Password", "copy": "Copy", "email": "Email", "closeAcc": "Close Account", "create": "Create", "from": "From", "download": "Download", "fromDate": "From Date", "domain": "School Readiness Domain", "age": "Age", "ageGp": "Age Group", "edit": "Edit", "delete": "Delete", "delSfy": "Deleted successfully.", "sendMessg": "Family Engagement", "draftCurriculum": "Draft Curriculum", "speList": "Specialist", "ok": "OK", "status": "Status", "fieldReq": "This field is required.", "ctmTag": "Portfolio Tags", "date": "Date", "source": "Source", "out": "Out", "gen": "Generate", "gender": "Gender", "group": "Groups", "Group": "Group", "save": "Save", "toDate": "To Date", "female": "Female", "language": "Language", "iep": "IEP/IFSP", "import": "Import", "iptOrSync": "Import/Sync", "inactive": "Inactive", "info": "Information", "basicInfo": "Basic Information", "analyRep": "Analytical Reports", "inptSerch": "Please input the book title you want to search.", "signIn": "Sign in", "lessons": "Lessons", "lesson": "Lesson", "boo": "Video Books", "book": "Book", "books": "books)", "childList": "Children List", "lessonsSetting": "Reflective Planning Settings", "mgAS": "Daily Health Card", "framework": "Framework", "male": "Male", "measure": "Measure", "learnMedia": "Learning Media", "myProFile": "My Profile", "minutes": "minutes", "none": "None", "children": "Children", "noBookVideos": "Oops, there are no video books for this title.", "noChild": "There are no children here.", "noShowAgn": "Do not show this again", "name": "Name", "domainName": "School Readiness Domain", "className": "Class Name", "childName": "Child's Name", "userName": "User Name", "centerName": "Site Name", "groupName": "Group Name", "normal": "Normal", "note": "Notes", "inkindNote": "Note", "nftions": "What's New", "skip": "<PERSON><PERSON>", "parent": "Parent", "to": "To", "parEnga": "Family Engagement", "pdf": "PDF", "pending": "Pending", "lsPlan": "Lesson Plan", "template": "Template", "weekName": "Week", "plsSaveEdit": "Please save your edits or the program will sign out automatically in 5 mins.", "teacher": "Teachers", "assPro": "Assessment Progress", "googleBook": "We provide a filtered and safer way to find appropriate books from the Google Book library with the highest security level setting. Although we are trying our hardest, you need to be aware that our filters are not 100% safe. Teachers should preview every book to be shared and flag or report any inappropriate books to us.", "remove": "Remove", "reset": "Reset", "report": "Engagement", "restore": "Rest<PERSON>", "checkRef": "Check Reference", "role": "Role", "select": "Select", "next": "Next", "site": "Site", "searchStaffName": "Search staff's name", "submit": "Submit", "upload": "Upload", "dashboard": "Dashboard", "theme": "Category", "time": "Time", "title": "Title", "allClass": "All Classes", "allWeeks": "All Weeks", "allCenter": "All Sites", "url": "URL", "view": "View", "views": "Views", "video": "Videos", "preview": "Preview", "disabled": "Disabled", "type": "Type", "description": "Description", "deleted": "Deleted", "areYouSignOut": "Are you sure you want to sign out?", "grade": "Age Group", "videoBook": "Video Book", "done": "Done", "empty": "Empty", "generate": "Generate", "searchRes": "Search result", "Miss": "Miss", "selectCenter": "Select Site", "loadingDataNow": "We are loading the data now.  Please wait a moment.", "approvalPENDING": "Pending", "approvalQueue": "Approval Queue", "selectAll": "Select All", "themeUper": "Theme", "activityTime": "Activity Duration", "on": "On", "off": "Off", "values": "Values", "active": "Active", "teacherRole": "Teacher", "clear": "Clear", "update": "Updated", "yes": "Yes", "no": "No", "noteReview": "Notes Review", "sortBy": "Sort by", "comment": "Comment", "noTeacher": "This class has no teacher.", "supportFiles": "Support PDF / PPT / Excel / Word format, up to 4 files", "attachment": "Attachment", "history": "History", "setSucs": "Saved successfully.", "channel": "Channel", "selectVideo": "Select {{selectVideoNum}} videos", "startTime": "Starting Time", "detail": "Detail", "progress": "Progress", "stop": "Stop", "comple": "Completed", "readMore": "Read more", "allNotifis": "See all the notifications", "agencyOwner": "Agency Owner", "child": "child", "Child": "Children", "reflectiveLessonPlanning": "Reflective Planning", "previous": "Previous", "months": "Months", "detailInfo": "Details", "childInfo": "Child Info", "settingType": "Settings", "teachingAssistant": "Teacher Assistant", "days": "Days", "noNotific": "No notifications!", "noChatGroup": "No chat group", "class": "Class", "Children": "Children", "webChat": "Cha<PERSON>", "state": "State", "drdpExport": "DRDP Setting", "isLoginOutTip": "Your account is logged in from another device. Log in now will log out you from the other device.", "searchStuBtn": "Search", "badRequest": "Bad Request", "logonFailure": "Logon failure", "accessDenied": "Access denied", "requestAddressError": "Request address error", "requestTimeout": "Request timeout", "serverInternalError": "Server internal error", "serviceNotImplemented": "Service not implemented", "badGateway": "Bad Gateway", "serviceUnavailable": "Service unavailable", "gatewayTimeout": "Gateway timeout", "httpNotSupport": "The HTTP version is not supported", "inKindReport": "In-Kind", "attendance": "Attendance", "event": "Event", "statistics": "Statistics", "attenDance": "Attendance", "UpdateTime": "Update time", "ratingPeriod": "Rating Period", "schoolMessage": "School Announcements", "events": "Events", "hearPortfolio": "Manage Portfolio", "ended": "Ended", "publish": "Publish", "unit": "Unit", "discardAct": "Discarded Activities", "inkindReport": "In-Kind", "ikonw": "Got it", "activityDescription": "Activity Description", "inkindAdmin": "Admins", "inkind": "In-Kind", "example": "Examples", "saveDraft": "Save as a draft", "Draft": "[Draft]", "athomeActivites": "At-Home Activities", "LessonHomeActivityPlaceholder": "Any home activities to engage children in learning outside of the classroom setting?", "volunteerActivites": "Volunteer Activities", "volunteer": "Volunteer", "deleteDomain": "Are you sure you would like to delete this domain? Parents and teachers will not see this domain or its activities after deletion.", "domainAbbreviation": "Domain Abbreviation", "sourceName": "Source name", "hours": "hours", "manageInkind": "In-Kind Settings", "navRatingPeriodText": "Rating Period Setup", "familyService": "Family Service Staff", "plan": "Weekly Lesson Planner", "ageGroup": "Age Group", "planCreator": "Creator", "pAddDLL": "Add DLL", "planPreview": "Preview", "plan_saveDraft": "Save as Draft", "planWeek": "Week", "planTheme": "Theme", "planTeacher": "Teacher", "planStatus": "Status", "pDraft": "Draft", "planRejected": "Revise", "planApproved": "Approved", "pEditDLL": "Edit DLL", "pConfirm": "Confirm", "infoNum": "Please select", "noSave": "Don't Save", "p_review": "Review", "new": "New", "switchClass": "Switch the class to the current rating period group.", "siteName": "Site Name", "value": "Value", "activities": "Activities", "lastWeek": "Last Week", "lgMediaManage": "Learning Media", "healthStatistics": "Health Card Stats", "unsubmit": "", "survey": "Survey", "GenieCourses": "Learning Genie Courses", "mileage": "Mileage", "pleaseSelect": "Please select", "activityAssignment": "Activity Assignment", "summaryReport": "Summary Report", "assignedTo": "Assigned to", "startDate": "Start Date", "mins": "mins", "parentPR": "Parent Progress Report", "childPR": "Child Progress Report", "duration": "Duration", "action": "Actions", "selectDate": "Select Date", "VisualHealthCheck": "Visual Health Check", "Tips": "Tips", "confirmed": "Confirmed", "notific": "Notifications", "knowledgeBase": "Knowledge Base", "files": "Files", "lowerAttach": "attachment", "lowerAttachs": "attachments", "later": "Later", "father": "Father", "mother": "Mother", "dataHub": "Data Hub", "drdpReport": "DRDP Report", "groupReport": "Group Progress Report", "readinessReport": "School Readiness Report", "classPlanReport": "Class Planning Report", "groupDetailReport": "Group Detail Report", "ratingCompletion": "Rating Completion", "race": "Race", "eld": "ELD", "total": "Total", "RespondingEarlier": "Responding Earlier", "RespondingLater": "Responding Later", "ExploringEarlier": "Exploring Earlier", "ExploringMiddle": "Exploring Middle", "ExploringLater": "Exploring Later", "BuildingEarlier": "Building Earlier", "BuildingMiddle": "Building Middle", "BuildingLater": "Building Later", "IntegratingEarlier": "Integrating Earlier", "IntegratingMiddle": "Integrating Middle", "IntegratingLater": "Integrating Later", "IT": "<PERSON><PERSON>", "PS": "Preschool", "K": "Kindergarten", "notification": "Notification", "selectMeasure": "You can select up to 3 measures.", "selectMeasureForOneWeek": "You can select up to 10 standards for one week.", "selectMeasureForTwoWeeks": "You can select up to 20 standards for two weeks.", "selectMeasureForMoreWeeks": "You can select up to 30 standards for three weeks or more.", "unitPlannerDomainMaxThree": "You can select up to 3 subjects.", "selectMeasureEight": "You can select up to 8 standards.", "selectMeasureTwenty": "You can select up to 20 standards.", "selectMeasuresOrStandards": "Select Standards", "selectMeasuresOrStandards2": "Please select standards", "SearchStandardOrMeasures": "Search", "standardOrMeasure": "Standard", "standardsOrMeasures": "Standards", "selectSubjects": "Please select subjects", "evaluationLessonDetailTitleAll": "Some of the standards (measures) you've selected and the activity duration you've adjusted might not align with this activity. To ensure a better learning experience, we recommend the following adjustments.", "evaluationLessonDetailTitleMeasure": "Some of the selected standards (measures) may not align well with this activity. We recommend reviewing and removing these standards to ensure better alignment.", "evaluationLessonDetailTitleTime": "We've noticed that the activity duration you've adjusted might not align with this activity. For an optimal experience, we recommend the following duration tailored to the age group and educational goals.", "addAttachFile": "Attach File", "tips": "Tips", "confirmTip": "Please confirm the DRDP views again and check the box below.", "es": "Exploring<br> Spanish", "shareWithParents": "Share with parents", "menuLesson2": "Reflective Lesson Planning", "lessonPlan": "Lesson Plan", "lesson2TabName2": "Assessment Insights", "lesson2TabName3": "Weekly Planning", "lesson2TabName4": "Foundations | Standards", "lessonLibraryTabName1": "Public Lessons", "lessonLibraryTabName2": "Agency-wide Lessons", "lessonLibraryTabName3": "My Lessons", "lessonLibraryTabName4": "Lessons Management", "lesson2NewLessonTitle": "Add New Lesson", "lesson2NewLessonFormLabelLessonName": "Lesson Name", "lesson2NewLessonFormLabelAgeGroup": "Age Group", "lesson2NewLessonFormLabelAgeGroupDeleteTips": "Are you sure you want to delete this age group? The implementation steps and EduProtocols template for this age group will also be deleted after confirming.", "lesson2NewLessonFormLabelCenterAgeGroupDeleteTips": "Are you sure you want to delete this age group? The implementation steps for this age group will also be deleted after confirming.", "lesson2NewLessonFormLabelPrepareTime": "Prepare Time", "lesson2NewLessonFormLabelActivityTime": "Activity Duration", "lesson2NewLessonFormLabelTheme": "Theme/Topic", "lesson2NewLessonFormLabelFramework": "Framework", "lesson2NewLessonFormLabelDomain": "Domains", "lesson2NewLessonFormLabelMeasures": "Measures", "lesson2NewLessonFormLabelObjectives": "Objectives", "lesson2NewLessonFormLabelMaterials": "Materials", "lesson2NewLessonFormLabelSteps": "Implementation Steps/Guides", "lesson2NewLessonFormPlaceHolderLessonName": "Lesson Name", "lesson2NewLessonFormPlaceHolderSelect": "Please select", "lesson2NewLessonFormPlaceHolderObjectives": "You can add specific and detailed objectives for both general students and students with special needs here.", "lesson2NewLessonFormPlaceHolderMaterials": "Materials required", "lesson2NewLessonFormPlaceHolderSteps": "Specific Instruction for", "lesson2NewLessonFormPlaceHolderCover": "Support images (under 3 MB) and videos (under 40 MB). <br/>Tips for the cover picture:  we advise you use a final lesson outcome picture or any representative images or small videos. It will be used as a visual cue for search. <br/>You might avoid close-up child face images for privacy concerns.", "lesson2NewLessonStepMediaUploaderTips": "Support images (under 3 MB) and videos (under 40 MB).", "lessons2LessonResources": "Family Resources", "lessons2LessonDLLTitle": "DLL Vocabulary and Phrases", "lesson2FailedLoadingMedia": "Failed to load", "yearsOld": "years old", "totalEntries": "Total entries", "lesson2CommentTitle": "Comments", "lesson2CommentTotalTitle": "Total comments", "lesson2CommentLessonAuthorTag": "Creator", "lesson2CommentReplyComment": "Reply", "lesson2CommentViewTitle": "View", "lesson2CommentHidePrefix": "<PERSON>de", "lesson2CommentSuffix": "reply", "lesson2CommentSSuffix": "replies", "lessons2CommentEndTitle": "The end", "lesson2CommentInputTips": "Add a public comment...", "lessons2LessonDetailGuide": "Specific Instruction for", "lessons2LessonBook": "Book", "lessons2LessonVideo": "Video", "lessons2LessonAttachments": "Attachments", "lessons2LessonDetailToAuthorName": "By", "lessons2LessonDetailTipInfo": "The instructional content provides one set of possibilities towards children development outcomes and measures. If you are experienced, you could modify your version of targeted behavior and skills (e.g. DRDP measures). ", "lessons2LessonLikeTipInfo": "Like", "lessons2LessonFavoriteTipInfo": "Add to Favorites", "lessons2CommentShowMore": "Show More", "lessons2LessonReplicate": "Replicate", "lessons2LessonDetailNewTab": "New Tab", "lessons2DeleteLesson": "Delete Lesson", "lessons2LessonEdit": "Edit", "lesson2LessonNameRequired": "Lesson name is required", "lessons2LessonPromote": "Add to Agency-wide Lessons", "lessons2LessonPromotionRemove": "Remove from Agency-wide Lessons", "lessons2TeacherLessonTagName1": "My Creations", "lessons2TeacherLessonTagName2": "My Favorites", "lessons2TeacherLessonTagName3": "Drafts", "lessons2LessonListLoading": "Loading", "lessons2LessonListSearchPlaceholder": "Search lesson name or standards", "assessmentCohortView": "Assessment Cohort View", "lessons2AdminLessonsSettingMappingTitle": "Alignment of DRDP to Other Frameworks", "lessons2RecycleDialogTitle": "Deleted Lessons", "lessons2SharedToAgency": "Added to agency-wide lessons", "lessons2AddReply": "Add a public reply...", "lessons2DeleteLessonTips": "This lesson has been deleted by the creator.", "lessons2Interests": "Interests", "lessons2Okay": "Okay", "lessons2Reply": "Reply", "lessons2Save": "Publish", "lessons2ShowMore": "Show more", "lessons2ShowLess": "Show less", "lessons2SupportImage": "Support images only.", "lessons2PermanentlyDeleteButtonTips": "Permanently delete", "lessons2RestoreTips": "Rest<PERSON>", "lessons2PermanentlyDeleteTips": "Are you sure you want to permanently delete this lesson?", "lessons2RestoreLessonTips": "Are you sure you want to restore this lesson?", "lessons2PermanentlyDeleteSuccessTips": "This lesson was deleted permanently.", "lessons2RestoreSuccessTips": "This lesson was restored successfully.", "lessons2PDF": "PDF", "lessons2DownloadOrExport": "Download/Export", "lessons2Download": "Download", "lessons2DownloadPDF": "Download PDF", "lesson2LessonPlanPDF": "Lesson Plan PDF", "lesson2LessonPlanWord": "Lesson Plan Word", "lessons2DownloadWord": "Download Word", "lessons2DownloadFile": "Download File", "lessons2SuccessfullyExported": "Successfully Exported", "lessons2SuccessfullyTitle": "This lesson has been successfully exported to your Google Drive.", "lessons2GoDriveButton": "View in Google Drive", "lessons2SaveDrive": "Export to Google Drive (.docx)", "lessons2DeleteTips": "Are you sure you want to delete this lesson?", "lessons2DeleteStatusAgencyTips": "Are you sure you want to delete this lesson? Please note that it will also be removed from the \"Agency-wide Lessons\" after confirming.", "lessons2EditLessonTitle": "<PERSON>", "lessons2RemoveSuccessTips": "Removed successfully.", "lessons2LikedTips": "Liked", "lessons2FavoriteTips": "Added to favorites", "commentDeleteTips": "Are you sure you want to delete this comment?", "commentDeleteShow": "This comment has been deleted.", "lessons2ImageUploadTips": "Images you upload can not exceed 3MB in size.", "lessons2VideoUploadTips": "Videos you upload can not exceed 100MB in size.", "lessons2FileUploadTips": "file you upload can not exceed 10MB in size.", "TeacherNoResult": "You haven't added any teachers yet.", "lessons2NoResult": "No data found!", "lessons2UnsupportedFileFormat": "Unsupported file format.", "lessons2UploadFailed": "File upload failed", "lessons2AgencyTagName1": "All Lessons", "lesson2LessonSave": "Saved successfully!", "lesson2Publish": "Published successfully.", "lessons2Hide": "<PERSON>de", "lessons2ShowAll": "Show all", "lessons2NoBookOrVideoBook": "Oops! No results found!", "sensitiveWordViolationMessage": "Your content has sensitive words ({{word}}), please try again with some different keywords.", "plan1": "Search theme", "plan2": "Create Weekly Planner", "plan3": "Date Range", "plan4": "Theme", "plan9": "Actions", "plan10": "No weekly planners yet!", "plan11": "Are you sure you want to delete this weekly planner?", "plan12": "Weekly planner was deleted successfully.", "plan13": "Submit for Approval", "plan14": "Recall", "plan15": "Weekly Reflections and Plans", "plan16": "No reflections yet!", "plan17": "You have exceeded the maximum character limit ({num}).", "plan20": "This field is required.", "plan21": "Are you going to review the next weekly planner? Please select the following:", "plan22": "Completed the last planner", "plan23": "Continue to the next planner", "plan24": "Yahoo! All weekly planners have been reviewed.", "plan25": "Reflections for Last Week", "plan26": "Remove", "plan27": "This row can't be removed.", "plan28": "Add New Row", "plan33": "Select Measures", "plan34": "Add Children", "plan35": "You haven't added any children to this class.", "plan36": "No data found.", "plan37": "Reason for revision", "plan38": "Please enter your comments here...", "plan39": "Added successfully.", "plan40": "Are you sure you want to delete this row?", "plan41": "Select Week", "plan42": "Please select", "plan43": "Any reflections and plans on the implementation of the lesson plan?", "plan44": "Add title name", "plan45": "Please input {rowTitle}!", "plan46": "Please select week!", "plan47": "Please select teachers!", "plan48": "Please add lessons or activities!", "plan50": "Weekly planner was submitted successfully!", "plan51": "Are you sure you want to recall this weekly planner?", "plan52": "Weekly planner was recalled successfully!", "plan54": "This weekly planner has been deleted.", "plan55": "This weekly planner has been reviewed by other admin.", "plan56": "This weekly planner has been reverted.", "plan57": "The system has detected no user activity for 10 mins, so you have exited the edit mode automatically. The changes you made have been saved.", "plan58": "Are you sure you want to switch to a new class? If you proceed, any added children will not be saved and any added measures may differ if the assessment frameworks of these two classes vary.", "plan59": " is editing this weekly planner.", "plan60": "Search Measure", "plan61": "Download File", "plan62": "Approve", "plan63": "Revise", "plan66": "Replicate", "plan67": "Are you sure you want to replicate this weekly planner?", "plan68": "Submit", "plan69": "Please remember to add children to your planner so that the system can generate observation note drafts for children automatically on the same day the lesson was implemented.", "plan70": "Add Reflection", "plan71": "View Reflection", "plan73": "Lesson Plan", "plan74": "Observation Notes", "plan75": "Any reflections on the implementation of the designed lesson? Any new interests or opportunities of learning from the children?", "plan76": "What could be the next steps?", "plan77": "Lesson Reflections and Plans", "plan78": "Reflections for This Week", "plan79": "Lesson Reflection", "plan80": "Child Development Reflection", "plan81": "Past Reflections", "plan82": "View observation notes", "plan83": "<PERSON>de", "plan84": "Are you sure you want to delete this reflection?", "plan87": "Please add reflections.", "plan88": "What are the top 10 interest areas my students have shown?", "plan89": "Reflections", "plan90": "Edit Reflection", "plan91": "Include sub-domain", "plan92": "Sub-domains: {subDomains}.", "plan93": "Reviewers' Notes", "plan94": "Assigned by {userName} on {date}.", "plan95": "On the same day the lesson is applied,<ul><li>the system could generate an observation note draft for each lesson, you can add observation notes as needed.  <li>will also assign DLL home-language homework to families if you include key vocabulary or phrases in this lesson. <li>parents will receive the at-home activity and books associated.", "plan96": "Don't ask again", "plan97": "Please select your class first.", "plan98": "This is the first planner.", "plan99": "This is the last planner.", "plan100": "View all reflections", "plan101": "Weekly Reflections", "plan102": "Date Range: ", "plan103": "Total reflections: ", "plan104": "Date Range", "plan105": "Weekly Reflections", "plan107": "Add Activity", "plan108": "Please choose one of the following:", "plan109": "Create Agency-wide Weekly Planner", "plan110": "Assessment Framework", "plan111": "Please select", "plan112": "Create Class-level Weekly Planner", "plan113": "Please select a site and class first.", "plan114": "Please select an assessment framework first.", "plan117": "Creator", "plan118": "Assessment Framework", "plan119": "Edit Weekly Planner", "plan120": "Select Class", "plan121": "Add Interpretation", "plan122": "Select a Sound Source", "plan123": "System Audio + Microphone Audio", "plan124": "Microphone Audio", "plan125": "Are you sure you want to leave now? The video you've recorded will not be saved after confirming.", "plan126": "Are you sure you want to delete this interpretation?", "plan127": "Easily share your insights with Video Planner Interpretation!", "plan128": "Create a narrated recording to guide new teachers through the planning process;", "plan129": "Help those who might be struggling with implementing a weekly planner.", "plan130": "Got it", "plan131": "View Interpretation", "plan132": "New recording", "plan133": "Delete", "plan134": "This feature is not supported by your device. You can still access this feature on Learning Genie Web Portal.", "plan135": "Note", "plan136": "Please note that the browser audio will not be recorded using the \"Window\" recording option. To record your browser audio, please select the \"Tab\" recording option and proceed with the Learning Genie website.", "plan137": "Are you sure you want to record a new interpretation? The new interpretation will replace the previous one after confirming.", "plan139": "Unable to access microphone", "plan140": "Learning Genie needs access to your microphone. Please click the camera icon {icon} in the browser address bar to allow Learning Genie to use your microphone.", "plan141": "Unfortunately, you won't be able to save your recorded video as another teacher has already added their interpretation to this weekly planner.", "plan142": "Are you sure you want to leave now? Any unsaved recording will be lost once you confirm.", "plan143": "This feature is not supported by your browser. You can still access this feature on Chrome.", "plan144": "Preview", "plan145": "Your interpretation is currently being recorded. Please stay on this page.", "plan146": "Move Activities", "plan147": "Back to Edit", "plan148": "<strong>\"Drag & Drop\"</strong> Feature for Weekly Planning", "plan149": "Activity reordering is made easy with our intuitive drag-and-drop feature, which automatically saves your changes in real time. Simply click <strong>\"Back to Edit\"</strong> to add lessons, measures, and children.", "plan150": "Got it", "plan151": "You can place up to 5 lessons in each cell.", "plan152": "{user}'s Weekly Planners", "plan153": "Create Weekly Planner Exemplar", "plan154": "Lesson Reflection", "plan155": "Activity/lesson name", "plan156": "Type or paste a Google Drive link, oneDrive link, or any other type of content link", "plan157": "Search Lesson Library", "plan158": "Add Third-party Lesson", "plan159": "Create a New Lesson", "plan160": "With our digital weekly lesson planning tool, you are able to:", "plan161": "Incorporate age-appropriate and developmentally-aligned lesson plan activities from our extensive Lesson Library, or use your own lesson content with links.", "plan162": "Plan group and individualized lessons linked to assessment and child development insights.", "plan163": "Reflectively plan weekly and individual lessons to effectively implement designed lesson plans for the upcoming week.", "plan164": "Data-driven Weekly Lesson Planning", "plan165": "Utilize the sidebar for assessment and child development insights to enhance your lesson planning for individual classes.", "plan166": "Access comments, feedback, and your own previously added notes conveniently.", "plan167": "Collapse or expand by clicking \"", "plan168": "\" for a streamlined experience.", "plan169": "Customize Weekly Planner Template", "plan170": "Customize your weekly planner template by removing or adding rows as desired.", "plan171": "Additionally, edit the title for each row according to your needs.", "plan172": "ELD Children (In blue color)", "plan173": "IEP Children (In yellow color)", "plan174": "Print", "plan175": "Reason for revision: ", "plan176": "Direct printing is currently not supported by this browser. We recommend downloading the PDF first.", "plan177": "Remove from Weekly Planner", "plan179": "Are you sure you want to leave now? Changes you made will not be saved after confirming.", "lessonCopySuccess": "Replicated successfully!", "lessonCopySuccessNeedBack": "Replicated successfully, please return to the weekly planner to view.", "planGuideSkip": "<PERSON><PERSON>", "planGuideNext": "Next", "planGuideDone": "Got it", "planUnitOverviewDrag": "Please drag lessons only within the same large/small group.", "ownerTip": "Please add a site and a class to your agency first.", "siteTip": "Please add a class to your site first.", "teacherTip": "You haven't been assigned to any class yet. Please contact your admin for class assignment.", "unlink": "Remove Link", "planGuide4": "Create Weekly Planner", "planGuide5": "Enter/select theme-aligned lesson activities, or add your own lesson content with links.", "planGuide6": "Add measures as needed.", "planGuide7": "Create your group by adding the children.", "planGuide8": "Customize weekly planner template", "planGuide9": "Click", "planGuide10": " to remove a row. ", "planGuide11": " to add an entire row or a row with separate daily cell.", "planGuide12": "You can edit the title name as needed.", "planCommentTitle": "Comments and Feedback", "teacherPlanCommentTitle": "Ask Anything", "planCommentPlaceholder": "Teacher could ask questions on implementation, domains, children's typical behavior, and any tips.", "adminPlanCommentPlaceholder": "Any comments or feedback on this planner? Any additional suggestions or guidance to support teachers' growth?", "planInterpretIntroTips": "Click the \"Play\" icon for admins or coaches planner interpretation.", "planNoteDeleteConfirmTips": "Are you sure you want to delete this note?", "planNoteNoData": "You haven't added any notes yet.", "planNoteAdd": "Add Notes", "planNoteAddTips": "Add your note here...", "planNoteTitle": "My Notes", "planNoteEditTitle": "Notes", "planResourcePreview": "Family Resources and DLL Preview", "planResourcePreviewTips": "On the same day lesson is applied, parents will receive the at-home activity and books associated, and the home-language homework as well.", "planResourcePreviewNoData": "No homework or family resources can be sent to parents.", "deletedSuccessfully": "Deleted successfully.", "showCoreMeasureOnly": "Show key measures only", "noCoreMeasureTip": "There are currently no key measures. Please turn off the \"Show key measures only\" switch to show all measures.", "domains": "Domains", "domainsitem": "Domain", "domainsitem1": "Measure", "measures": "Measures", "standards": "Standards", "weekReflection": "Weekly Reflections and Plans", "preWeek": "Previous week", "nextWeek": "Next week", "growthMeasures": "Growth Measures During the Last Rating Period ", "domainStats": "Domain Score Statistics During Last Rating Period ", "meanScore": "Median Level", "benchmark": "Benchmark", "above": "At or Above", "below": "Below", "delBenchmarkView": "Deleted successfully.", "nodataTip": "No data found!", "noReflection": "You haven't added any reflections and plans yet.", "classOverView": "Class Overview", "childStrengthGrowthView": "Child Strength/Growth View", "interestView": "Interest View", "observationStatus": "Observation View", "DLL": "DLL", "addDLLTitle": "Subject Line", "selectLanguage": "Select Language", "addDLLContentTitle": "Key Vocabulary and Phrases", "translate": "Translate", "advancedSettings": "Advanced Settings", "settingDataSend": "Schedule Send", "requestSubmit": "Request parents to submit recording of practice", "editTip": "We noticed that you've modified the vocabulary or phrases, but the translations haven't been updated. Are you sure you want to publish it now?", "selectYourLanguage": "Select Your Language", "searchLanguage": "Search language", "selectedLanguages": "Selected Languages", "otherLanguages": "Other Languages", "receiveNewFeedback": "You received {num} new comment.", "selectedLanguageNum": "Selected languages: {num}", "limitInputNum500": "You have exceeded the maximum character limit (500). ", "limitInputNum2000": "You have exceeded the maximum character limit (2000). ", "limitSelectLanguageNum": "You can select up to 10 languages.", "pleaseSelectLanguage": "Please select languages first.", "notEditDLL": "This homework has been assigned to families, so you can not change the settings.", "createInfo": "Created by {people} on {date} at {time}", "lastUpdatedInfoByUser": "Updated by {people} on {date} at {time}", "selectDateAndTime": "Pick date & time", "DLLAddSuccess": "Added successfully.", "singleComment": "Comment", "oneComment": "1 Comment", "moreComment": "{num} Comments", "fieldIsRequired": "This field is required.", "deleteDLLTip": "Are you sure you want to delete this item?", "deleteDLLSuccess": "Deleted successfully.", "receiveMoreNewFeedback": "You received {num} comments.", "noTranslateByAdd": "Vocabulary or key sentences haven't been translated yet.", "addDLLContentTip": "Please enter key vocabulary and phrases for this DLL session. It will be translated into selected home language and shared with parents for practice at home.", "play": "Play", "searchDLL": "Search subject line, vocabulary or phrases", "batchFillOutForm": "Batch Fill Out Form", "signinOrOutReview": "Sign-in/out Review", "attendanceReview": "Attendance Review", "weeklyPlannersManagement": "Weekly Planners Management", "createVirtualShadowList": "Virtual Shadow List", "virtualShadowingDetails": "Virtual Shadowing Details", "weeklyPlannerDetails": "Weekly Planner Details", "weeklyPlannerExemplarDetails": "Weekly Planner Exemplar Details", "creatorOfPlanner": "Creator of Planner", "shadowedPlanner": "<PERSON><PERSON> Planner", "totalCount": "Total", "assignedTime": "Assigned Time", "recipients": "Recipients", "openRate": "Open Rate", "weeklyPlannerList": "Weekly Planner List", "shadowedList": "Shadowed List", "assignedBy2": "Assigned by", "entireSet": "Entire Set", "createVirtualShadow1": "Create Virtual Shadow", "createVirtualShadow2": "Create Virtual Shadow", "selectedWeeks": "Selected Weeks", "shareWith": "Assigned to", "shareComment": "Comment", "commentTip": "Any guidelines or suggestions for teachers on the creation or implementation of weekly planners?", "shareSuccess": "Created successfully.", "noPlanTip": "No weekly planners found during this time period.", "openSts": "Weekly Planner Open Statistics", "shareOpened": "Opened ({num})", "shareNotOpened": "Not Opened ({num})", "commentAndFeedback": "Comments and Feedback", "sureRecallShadowed": "Are you sure you want to recall the shadowed planners from all recipients?", "recalledSuccessfully": "Recalled successfully.", "noShadowedPlannerYet": "No shadowed planners yet!", "selectedOtherLanguageNum": "Other languages: {num}", "onlySendToDLLParent": "DLL parents only", "sendToAllParent": "All parents", "noDLLChildren": "No DLL children in this class.", "dllEnglishTitle": "Key Vocabulary and Phrases", "dllReview": "DLL Review", "noPlayContent": "Translations cannot be empty.", "dllTranslatePlaceholder": "Translations", "DLLVocabularyAndPhrases": "DLL Vocabulary and Phrases", "DLLHelpTwo": "2. On the same day this lesson in your weekly planner is applied, the system will assign home-language homework to families. You can also view multiple DLL entries in sequence, which is helpful for key vocabulary and phrases training in the classroom.", "DLLSettingTitle": "Assign Home-language Homework to Families", "DLLSettingInfo1": "On the same day the lesson is applied, the system will assign DLL home-language homework to all families if you include key vocabulary or phrases in this lesson.", "DLLSettingInfo2": "Note: The system will not assign home-language homework to families if you do not select languages here.", "DLLSettingTipInfo1": "The vocabulary or phrases can be translated into the languages you've selected here.", "lessons2LessonDLLAndTitle": "Key Vocabulary and Phrases", "lessons2LessonDLLLanguageSelect": "Please select", "lessons2LessonDLLSettingHelp1": "1. You can add DLL vocabulary or phrases here to help improve child's dual language proficiency.", "lessons2AddDLLTotal": "Total entries: ", "courseName": "Course Name", "learningGenieCourses": "LG Courses", "lgCourse": "Learning Genie Courses", "noteTip": "Note", "mergedAgeGroup1": "This view automatically displays combined data based on your previous setup of either 9-18 months, 19-36 months, or both, and cannot be edited directly.", "mergedAgeGroup2": "This view automatically displays combined data based on your previous setup of either 3 years, 4-5 years, or both, and cannot be edited directly.", "updateAttendance": "Update Attendance", "clTitle": "ClassLink API Sync", "cleverTitle": "Clever API Sync", "cleverSubTitle": "Clever API Sync", "mhsTitle": "GoEngage API Sync", "mhsSubTitle": "Sync your school data directly from your SIS through GoEngage APIs.", "importTitle": "Import/Sync", "show": "Show", "quizShowAll": "Show all", "hide": "<PERSON>de", "failed": "Failed", "infantSleepCheck": "Infant Sleep Check", "dllResourceDeleteTip": "Are you sure you want to delete this?", "dllWelcomeContent1": "To add DLL activities and assign home-language homework to families, click the \"Add DLL <br> Activity\" button below or select DLL vocabulary from the DLL Library.", "custom": "Custom", "dllShareSuccess": "Shared successfully", "allDllActivities": "All DLL Activities", "totalEntriesNum": "Total entries: {count}", "dllActivitiesByStaff": "DLL Activities by Staff", "dllVocabularyBySubjects": "DLL Vocabulary by Subjects", "dllFromLesson": "From Weekly Lesson Planning", "dllFromDllLibrary": "From DLL Library", "inputKeyVocabulary": "Please input a key vocabulary or phrase here.", "selectImage": "Select Image", "addNewSubject": "Add New Subject", "subjectName": "Subject Name", "inputSubjectName": "Please enter your subject name here.", "dllVocabulary": "DLL Vocabulary", "shareDll": "Share", "shareDllSelectTip": "Please select the most appropriate option", "shareOnlyDllParent": "Share with DLL parents only", "selectedTotalDllVocabulary": "Selected vocabulary/ phrases: ", "selectOneVocabulary": "Please select at least one vocabulary or phrases.", "selectClassTitle": "Please select your class", "selectedDllVocabularyNum": "DLL vocabulary and phrases ({count})", "confirmAndShare": "Confirm and share", "saveSuccess": "Saved successfully.", "dllLibrary": "DLL Library", "addDllActivity": "Add DLL Activity", "noStaffInCenter": "No staff were found in this site.", "dllImageUploadTips": "Images you upload can not exceed 10MB in size.", "dllSearch": "Search", "selectedDllVocabularyNoNum": "DLL vocabulary and phrases", "noChildrenInClass": "No children in this class.", "guideHomeTitle": "View Your Child's Daily Activities", "guideHomeContent": "Available to see what your child is doing at school and what activities are coming up here.", "guideChatTitle": "Chat With Your Child's Teacher", "guideChatContent": "You can communicate with your child's teacher without any barriers, and work together to better support your child's learning.", "VideoGuide": "Video Guide", "ContactUs": "Contact Us", "SchoolMessage": "School Message", "helpAndResources": "Help And Resources", "donations": "Donations", "inKindGoals": "In-Kind Goals", "coverExternalMediaUploadSize": "Support images (under 10MB) and videos (under 100MB);", "uploadViaURL": "Upload via URL", "uploadExternalMediaTip1": "You can upload an image or a Vimeo/Youtube video via URL.", "vimeo": "Vimeo video ", "externalMediaUrlInputTip": "Please enter/paste an URL here.", "ResolutionFailed": "Resolution failed.", "materialAndStepDescription": "Available to upload an image or a video via URL.", "saveUrlTip": "Upload failed. Please try again later.", "ShowDomainOrMeasureDescription": "Show domain/measure description ", "showStandardDescription": "Show standard description", "fileDownloadTip": "Note", "fileDownloadConfirm": "Okay", "fileLinkSendToEmail": "The download link has been sent to your email. Please check inbox and download. Additionally, you can download via the web portal.", "selectResourceNum": "Selected ({num})", "curriculum": "Curriculum", "curriculum1": "My Favorites", "curriculum2": "Deleted Curricula", "curriculum3": "Add unit to weekly plan", "curriculum4": "by ", "curriculum5": "Domain", "curriculum6": "Units", "curriculum7": "Weeks", "curriculum8": "Activities", "curriculum9": "Description", "curriculum10": "Video Introduction", "curriculum11": "Key Resources", "curriculum12": "Units", "curriculum14": "Books List", "curriculum15": "Key Vocabularies", "curriculum16": "Printables", "curriculum17": "Activities List", "curriculum18": "Assessment Standards/Measures List ", "curriculum19": "Overview and Arc of Unit", "curriculum20": "Week {num}", "curriculums21": "Apply Curriculum to Classroom", "curriculum23": "Select Classroom", "curriculum24": "Setup Start Date", "curriculum25": "Select Week", "curriculum26": "Calendar", "curriculum27": "Holidays", "curriculum28": "Unit", "curriculum30": "My Curriculum", "curriculum32": "Curriculum Name", "curriculum34": "Describe this curriculum briefly here", "curriculum35": "You can upload up to 3 videos (each under 100MB)", "curriculum36": "Admin", "curriculum37": "This staff still needs to add weekly planners.", "curriculum40": "Select Units and Weeks", "curriculum41": "All Units All Weeks", "curriculum42": "Submitted successfully!", "curriculum43": "{num1} out of {num2} planners was submitted successfully.", "curriculums43": "{num1} out of {num2} planners were submitted successfully.", "curriculum44": "Status", "curriculum45": "Submitted", "curriculum46": "Unsubmitted", "curriculum47": "Proceed with the next planner", "curriculum48": "Edit Exemplar", "curriculum49": "Weekly Planner Exemplar", "curriculum50": "Creator", "curriculum51": "Published", "curriculum52": "Add Tag", "curriculum53": "Weekly Planning", "curriculum54": "Select Weekly Planner", "curriculum55": "Add Weekly Planner", "viewsample": "View Sample", "viewsample1": "Identified Domain", "viewsample2": "1-2 sentences about how unit addresses this DRDP domain", "viewsample3": "Add", "curriculum56": "Generate Materials List", "curriculum57": "Are you sure you want to upload a new weekly planner? The new planner will replace the previously selected weekly planner after confirming.", "curriculum58": "Confirmation", "curriculum59": "Confirm", "curriculum60": "Cancel", "addLessonPlan": "Add Lesson Plan", "planItemTitle": "Title", "planItemLink": "Link", "curriculum61": "Are you sure you want to delete this planner (Week {week})? All resources of this planner will also be deleted after confirming.", "curriculum62": "Are you sure you want to leave now? {num} unsubmitted planner will be saved as a draft after confirming.", "curriculums62": "Are you sure you want to leave now? {num} unsubmitted planners will be saved as  drafts after confirming.", "curriculum64": "Drafts", "curriculum65": "Add Curriculum", "curriculum66": "Please select planner first.", "curriculum67": "Please setup start date first.", "curriculum68": "Please select week.", "curriculum69": "Please create your own activities for each center, such as Math, Blocks, Visual Art, etc.", "curriculum70": "You haven't generated a materials list for Week {num2}, Unit {num1}, are you sure you want to publish?", "curriculum71": "Publish Anyway", "curriculum72": "Are you sure you want to delete {name}?", "curriculum73": "You still need to add materials.", "curriculum74": "Please select a framework first.", "curriculum75": "We noticed that some changes are made to activities. Please try again after re-generating the materials list.", "curriculum76": "Missing weekly planner/required fields in Week {num1}, Unit {num2}", "curriculum77": "Missing required fields in Unit {num1}", "curriculum78": "Missing required fields in Curriculum", "curriculum79": "Please enter a name for each tag.", "curriculum80": "Please enter a name for each category.", "curriculum81": "Curriculum Name", "curriculum83": "Setup Start Date", "curriculum84": "Tag name", "curriculum85": "Custom tag name", "curriculum86": "Tag Name", "curriculum88": "Curriculum name is required.", "curriculum89": "Are you sure you want to delete this unit (Unit {unit})? All resources of this unit will also be deleted after confirming.", "curriculum90": "OK", "curriculum91": "Unit Plan", "curriculum92": "Note: ", "curriculum93": "1. You can select a start date in future weeks only.", "curriculum94": "2. The start date will be automatically set to Monday of the week you have selected.", "curriculum96": "Edit Weekly Planner", "curriculum97": "Preview Exemplar", "curriculum98": "Create Exemplar", "curriculum99": "Are you sure you want to delete this center?", "curriculum101": "Edit <PERSON>", "curriculum102": "Unit {num}", "curriculum103": "Week {num}", "curriculum104": "Brief 2-3 sentence description of what the unit is about and what children will explore and learn about", "curriculum105": "Tips for the cover picture: we advise you use a final lesson outcome picture or any representative images or small videos. It will be used as a visual cue for search.", "curriculum106": "You might avoid close-up child face images for privacy concerns.", "curriculum107": "Please create your own activities for each station, such as Art, Math, Library, etc.", "imageLoadFailed": "Load Failed", "unitweek": "Unit", "unitweek1": "Week", "unitweek2": "Books", "Monday": "Monday", "moreButten": "More", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "selecterror": "Please select an activity first.", "addResourceSelectActivityTip": "Select Activity", "addVideoBook": "Add Video Book", "materialsList": "Materials List", "viewAll": "View All", "addMaterial": "Add Material", "resourceListNote": "Note: all lesson-related resources will be listed below by default. You can make modifications if needed.", "addBook": "Add Book", "addKeyVocabulary": "Add Key Vocabulary", "selectDomains": "Select Domains", "addTo": "Add to", "selectActivity": "Select Activity", "selectManuallyActivityTip": "(you can only select a manually input activity here)", "previewAndSave": "Preview and Save", "inputBookTitleTip": "Input title here...", "resources": "Resources", "centerWeek": "Centers", "selectvocabulary": "Please select vocabulary first.", "feedbackSuccess": "Thank you for your feedback.", "positiveFeedback": "Positive Feedback", "negativeFeedback": "Negative Feedback", "planpre": "Weekly Planner Preview", "inKindSubmission": "In-Kind Submission", "ratify100": "", "typicalBehaviors": "Typical Behaviors and Observation Tips", "lessonDeleteMeasure": "Are you sure you want to delete this standard (measure)? Teaching tips, as well as typical behaviors and observation tips aligned with this standard (measure), will also be removed upon confirmation.", "lessonChangeFramework": "Are you sure you want to switch the assessment framework? Child's typical behaviors of selected measures will be deleted after confirming.", "lessonTypicalPlaceholder": "Children's typical behaviors at this age", "teachingTipsForStandards": "Teaching Tips for Standards", "personalization": "Personalization", "personalizationTip": "Empower your K-12 unit plans with a graduate profile lens that fosters both academic and personal growth, aligning each stage with the Portrait of a Graduate to develop life-ready competencies and meet your teaching goals seamlessly.", "tailorTitle": "Tailor Unit Plans with Portrait of a Graduate", "lessonTailorTitle": "Tailor Lesson Plans with Portrait of a Graduate", "unitPlannerStep3SaveConfirmMessage": "Your recent changes haven't been saved. Would you like to save before leaving?", "unitPlannerStep3AtLeastOneRubric": "Please complete the graduate profile attributes and expectations first.", "generationInProgressTip": "Your Content is still being generated, Do you want to save your progress before exiting?", "generationInProgressConfirm": "Save and Exit", "generationInProgressCancel": "Exit", "learnerProfile": "Portrait of a Graduate", "learnerProfileFocus": "Graduate Profile Focus", "learnerProfileFocusGeneratedSuccess": "Graduate profile focus was generated successfully!", "learnerProfileFocusTip": "Please complete the graduate profile focus.", "learnerProfileCreateTips": "Please review the attributes and expectations below to ensure they align with your teaching goals.", "learnerProfilePlaceholder": "Enter the skills and attributes students should develop by graduation to auto-generate tailored expectations for each grade band. These competencies will be integrated into the design of each unit, keeping you aligned with your teaching goals.\ne.g.\n1. Constructive Collaborator\n2. Empowered Learner\n3. Creative Problem Solver", "lessonLearnerProfilePlaceholder": "Enter the skills and attributes students should develop by graduation to auto-generate tailored expectations for each grade band. These competencies will be integrated into the design of your lesson plan, keeping you aligned with your teaching goals.\ne.g.\n1. Constructive Collaborator\n2. Empowered Learner\n3. Creative Problem Solver", "learnerProfileNotSupport": "This feature only applies to K through Grade 12, including mixed age groups.", "learnerProfileSetupNow": "Setup now", "learnerProfileUseTip": "Empower your unit plans with a focused approach that fosters both academic and personal growth, aligning each stage with the Portrait of a Graduate to build life-ready competencies and achieve your teaching goals.", "lessonLearnerProfileUseTip": "Empower your lesson plans with a focused approach that fosters both academic and personal growth, aligning each stage with the Portrait of a Graduate to build life-ready competencies and achieve your teaching goals. ", "learnerProfileGenerateFailedTip": "We are currently unable to process your request. Please enter valid PoG attributes and try again.", "learnerProfileTipsFailed": "Generation unsuccessful, please try again.", "learnerProfileNotEnabled": "This feature is not enabled for this unit.", "learnerProfileNotSetTipsLong": "The graduate profile for {grade} hasn't been set up yet.", "learnerProfileNotAdminTips": "The graduate profile for this age group hasn't been set up yet. Please contact your administrator to set it up.", "learnerProfileGenerateTip": "You're all set to generate expectations for {grade} because this grade has been pre-selected. Click on this button to proceed", "learnerProfileSaveTips": "We noticed you've updated the Portrait of a Graduate. Would you like to regenerate the attributes and expectations before proceeding?", "learnerProfileOnlySave": "Save Only", "learnerProfileAttributes": "Attributes", "learnerProfileAttributePlaceholder": "Attribute", "learnerProfileStandards": "Standards", "learnerProfileStandardsPlaceholder": "Standard", "learnerProfileExpectations": "Expectations", "learnerProfileStudentExpectations": "Student expectations", "learnerProfileGenerateStudentExpectations": "Generate student expectations", "learnerProfileGenerateStudentExpectationsTooltip": "Clarify what each Portrait of a Graduate attribute looks like at every grade band—turning abstract competencies into specific, observable expectations that make your vision actionable across the system.", "learnerProfileAddAttribute": "Add Attribute", "learnerProfileDelete": "Delete", "learnerProfileAttributeTitleAlreadyExistsTip": "Attribute name already exists.", "learnerProfileStandardTitleAlreadyExistsTip": "Standard name already exists.", "learnerProfileDeleteItemTips": "Are you sure you want to delete this item?", "learnerProfileInsertRowAbove": "Insert 1 row above", "learnerProfileInsertRowBelow": "Insert 1 row below", "learnerProfileGenerateByGradeBandsTip": "To better align expectations with students’ growth in PoG attributes, we’ve shifted from grade-specific to broader grade band expectations (K–2, 3–5, 6–8, 9–12), supporting more cohesive, age-appropriate learning goals. Would you like to regenerate expectations by grade band?", "learnerProfileGenerateByGradeBandsTitle": "Student Expectations by Grade Band", "learnerProfileRenerateByGradeBands": "Regenerate Student Expectations by Grade Band", "learnerProfileSelectLimitForLesson": "You can only select up to 3 PoG attributes.", "learnerProfileSelectLimitFor1Week": "You can only select up to 3 PoG attributes for one week.", "learnerProfileSelectLimitFor2Week": "You can only select up to 6 PoG attributes for two weeks.", "learnerProfileSelectLimitFor3Week": "You can only select up to 9 PoG attributes for three weeks or more.", "managePermission": "Manage Permission", "allGrades": "All grades", "pleaseSelectGrade": "Please select grade", "pleaseSelectGradeBand": "Please select grade band", "k_5_6": "K (5-6)", "grade_1": "Grade 1", "grade_2": "Grade 2", "grade_3": "Grade 3", "grade_4": "Grade 4", "grade_5": "Grade 5", "grade_6": "Grade 6", "grade_7": "Grade 7", "grade_8": "Grade 8", "grade_9": "Grade 9", "grade_10": "Grade 10", "grade_11": "Grade 11", "grade_12": "Grade 12", "mixed_age_group": "Mixed age group", "teachingTipsPlaceholder": "Teaching tips aligned with this standard", "customizeUnitFoundationModules": "Customize Unit Foundation Structures", "inKindApproval": "In-Kind Approval", "setUpByGrant": "Setup Now", "IEPComments": "Please provide more specific details about the child's unique needs and challenges for tailored support.", "IEPGoal": "Please enter child's IEP goals here...", "CreatedbyNum": "Created by ({num})", "OtherPlannerCreators": "Other Planner Creators ({num})", "lessonDetail": "Lesson Details", "messages": "Messages", "manuallyAdded": "Manually Added", "dropOffNote": "Drop-off Note", "collapse": "Collapse", "or": "or", "removed": "Removed", "exportRatingsToSis": "Export Ratings to SIS", "saveSetting": "Save & Schedule Export", "settings": "Settings", "attendanceDHC": "Attendance/DHC", "engagement": "Engagement", "assessmentReport": "Assessment Report", "cohortProgressReport": "Cohort Progress Report", "otherReport": "Other Report", "dll": "Dual Language Learners", "deleteSuccess": "Deleted successfully.", "dragItemTip": "Drag to Reorder", "lessonPublishAndRecommendAgency": "Lesson published and saved to agency-wide lesson list successfully.", "plannerReviseNewTip": "Reviewed as \"Revise\"", "planHasBeenDeleted": "The weekly plan has been deleted.", "saveToAgencyWideLesson": "Save to Agency-wide Lessons", "generateActionPlanErrorMessage": "We are currently experiencing a temporary service interruption. Please try again later.", "unableToProcessRequestAdjustLessonPlan": "We are currently unable to process your request. Please adjust your lesson plan adaptation ideas and try again.", "unableToProcessEnhanceAdjustLessonPlan": "We are currently unable to process your request. Please adjust your enhancement ideas and try again.", "unableToProcessRequestAdjustDescription": "We are currently unable to process your request. Please adjust your description and try again.", "generaetLessonDetailAdjustBookTip": "Please update the non-existent book(s): '{books}', or we will recommend an alternative for the lesson.", "generaetLessonDetailAdjustBookEdit": "Edit", "generaetLessonDetailAdjustBookContinue": "Continue", "unableToProcessRequestAdjustDescription2": "We are currently unable to process your request. Please adjust your description and try again.", "assistantToolbarGuide1": "Enter Your Lesson Plan", "adaptationIdeas": "Adaptation Ideas", "assistantToolbarGuide2": "Enter or paste any lesson plan or a brief activity description.", "assistantToolbarGuide3": "Explore the built-in samples for inspiration and a quick experience.", "assistantToolbarGuide4": "Next", "assistantToolbarGuide10": "Back", "assistantToolbarGuide11": "Got it", "unitPlannerGuideRevolutionize": "Revolutionize Unit Planning with Magical Auto Generation", "unitPlannerGuideSimplifies": "Simplifies unit planning through auto generation, reducing your workload by 60%;", "unitPlannerGuideSeamlesslyUnit": "Seamlessly aligns with DRDP, PTKLF, CCSS, and state-specific standards;", "unitPlannerNewUnit": "New Unit", "unitPlannerYouCurrently": "You currently do not have permission to add unit plans. Please contact your administrator.", "unitPlannerEmpty1": "Enhances lesson plans with children's typical behaviors, connecting planning to assessment for both DRDP and state standards;", "unitPlannerEmpty2": "Incorporates UDL and CLR Strategies for each lesson plan, fostering an inclusive and diverse classroom environment.", "unitPlannerEmpty3": "Easily apply unit planners to your classroom for increased teaching efficiency and consistency.", "unitPlannerEmpty4": "You haven't completed any units yet.", "unitPlannerEmpty5": "There is no corresponding student information!", "unitPlannerWeeklyWeeks": "Weeks", "unitPlannerAdditionalInformation": "Additional Information (Partial)", "unitPlannerWeeklyActivities": "Activities", "unitPlannerWeeklyAgeGroup": "Age Group", "unitPlannerWeeklyUpdatedOn": "Updated on", "unitPlannerWeeklyAddUnit": "Add unit to weekly plan", "unitPlannerWeeklyEdit": "Edit", "unitPlannerStep1UnitFoundations": "Unit Foundations", "unitPlannerStep1HaventGeneratedTheme": "You haven't generated the Weekly Theme & Overview for Week {week}.", "unitPlannerStep1ConfirmAll": "We noticed that you haven't confirmed the Weekly Theme & Overview for all weeks. Would you like to confirm them all?", "unitPlannerStep1DropImage": "Drop/select your Jpg, Png, Jpeg files here", "unitPlannerStep1WeeklyTheme": "Weekly Theme & Overview", "unitPlannerStep1LessonPlanIdeas": "Lesson Plan Ideas & Details", "unitPlannerStep1UnitName": "Unit Name", "unitPlannerStep1UnitDescription": "Unit Description", "unitPlannerStep1PleaseDescribeTheUnit": "Please tell us about the unit you have in mind or list the books you plan to use. This way, we can give you personalized recommendations.", "unitPlannerStep1Grade": "Grade", "unitPlannerStep1Weeks": "Weeks", "unitPlannerStep1Language": "Language", "unitPlannerStep1AssessmentFramework": "State Standards", "unitPlannerStep1Standards": "Subjects", "unitPlannerStep1Standards2": "Subjects/Domains", "unitPlannerStep1MeasureAutoAdapt": "Auto-adapted", "unitPlannerStep1MeasureAutoAdaptTip": "The system automatically recommends the most appropriate standards based on your selected subjects.", "unitPlannerStep1MeasureSpecified": "Specific Standards", "unitPlannerStep1MeasureSpecifiedTip": "You can manually choose the standards to address in this unit.", "unitPlannerStep1CreateLocalizedCurriculum": "Create localized curriculum", "unitPlannerStep1Country": "Country", "unitPlannerStep1State": "State", "unitPlannerStep1City": "City", "unitPlannerStep1Generate": "Generate", "unitPlannerStep1Regenerate": "Regenerate", "unitPlannerStep1UnitTrajectory": "Unit Trajectory", "unitPlannerStep1Concepts": "Concepts", "unitPlannerStep1GuidingQuestions": "Guiding Questions", "unitPlannerStep1Save": "Save", "unitPlannerStep1Next": "Next", "unitPlannerUpFoundation1": "We detected that you updated the Unit Foundation module.", "unitPlannerUpFoundation2": "Choose how you want to regenerate the content:", "unitPlannerKeepFoundation": "Keep Current Module: Regenerate content based on the old module.", "unitPlannerNewModuleFoundation": "Update Module: Regenerate content based on the new module.", "unitPlannerStep1Under20M": "under 20MB", "unitPlannerStep1CoverImageGenerationUnsuccessful": "The cover image was not generated successfully. Please try again. Alternatively, you can upload one manually.", "unitPlannerStep1RegenerateConfirmation": "Are you sure you want to regenerate? This action will overwrite the current content.", "unitPlannerStep2PleaseReview": "Please review the Weekly Theme & Overview carefully before proceeding to the next step.", "unitPlannerStep2RegenerateAll": "Regenerate All", "unitPlannerStep2WeekX": "Week {week}", "unitPlannerStep2Theme": "Theme", "unitPlannerStep2Overview": "Overview", "unitPlannerStep2Books": "Books", "unitPlannerStep2ThemePlaceholder": "Please input the main topic or focus for this week.", "unitPlannerStep2OverviewPlaceholder": "Briefly describe the key learning goals and activities for the week.", "unitPlannerStep2BooksPlaceholder": "Please enter the titles of books you plan to explore this week.", "unitPlannerStep2Rubrics": "Portrait of a Graduate", "unitPlannerStep2Back": "Back", "unitPlannerStep2ConfirmNext": "Confirm & Next", "unitPlannerStep3LargeOrSmallGroup": "Large/Small Group", "unitPlannerStep3LargeGroup": "Large Group", "unitPlannerStep3SmallGroup": "Small Group", "unitPlannerStep3ConfirmAll": "Confirm All", "unitPlannerStep3Confirm": "Confirm", "unitPlannerStep3Centers": "Centers", "unitPlannerStep3Settings": "Settings", "unitPlannerStep3Stations": "Stations", "unitPlannerStep3GenerateStationIdeas": "Generate Activity ldeas", "unitPlannerStep3GenerateCenterIdeas": "Generate Center Ideas", "unitPlannerStep3ProceedNextWeek": "Next Week", "unitPlannerStep3GenerateCenterActivity": "Proceed to Center Activities", "unitPlannerStep3GenerateDetailedLessonPlans": "Generate Detailed Lesson Plans", "unitPlannerStep3Publish": "Complete", "unitPlannerStep3SavedSuccessfully": "Saved successfully!", "unitPlannerStep3SavedUnsuccessfully": "Save unsuccessful.", "unitPlannerStep3GeneratedSuccessfully": "Generated successfully!", "unitPlannerStep3GeneratedModificationsOverwriting": "Any modifications will prompt the system to regenerate your lesson plan details, overwriting the existing version. Would you like to proceed?", "unitPlannerStep3ConfirmationLessonTitle": "Lesson Title", "unitPlannerStep3ConfirmationActivityType": "Activity Type", "unitPlannerStep3ConfirmationMeasures": "Standards", "unitPlannerStep3ConfirmationActivityDescription": "Activity Description", "unitPlannerStep3ConfirmationCenterName": "Center Name", "unitPlannerStep3ConfirmationConfirmGenerate": "Confirm & Generate", "unitPlannerStep3CenterRotationCenterRotationSettings": "Center Rotation Settings", "unitPlannerStep3CenterRotationStationRotationSettings": "Station Rotation Settings", "unitPlannerStep3CenterRotationDefaultSettings": "<PERSON><PERSON><PERSON>", "unitPlannerStep3CenterRotationApproachEnsures": "This approach ensures both diversity in center activities and minimizes your workload.", "unitPlannerStep3StationRotationApproachEnsures": "This approach ensures both diversity in station activities and minimizes your workload.", "unitPlannerStep3CenterRotationTheSystemWillAutomatically": "The system will automatically introduce 2 new center activities each week, starting from Week 2, while maintaining the continuity of the rest from the previous week. The Library & Listening Center activities may vary weekly depending on the books involved.", "unitPlannerStep3CenterRotationTheSystemWillAutomaticallyTemp": "The system will automatically introduce 2 new center activities each week, starting from Week 2, while maintaining the continuity of the rest from the previous week. The Library/Quiet Area activities may vary weekly depending on the books involved.", "unitPlannerStep3StationRotationTheSystemWillAutomatically": "The system will automatically introduce 2 new station activities each week, starting from Week 2, while maintaining the continuity of the rest from the previous week. The Library Station activities may vary weekly depending on the books involved.", "unitPlannerStep3CenterRotationCustomSettings": "Custom Settings", "unitPlannerStep3CenterRotationPleaseSelectTheCenters": "Please select the centers you want to rotate. Changes will only occur among these centers each week.", "unitPlannerStep3CenterRotationPleaseSelectTheStations": "Please select the stations you want to rotate. Changes will only occur among these stations each week.", "unitPlannerStep3CenterRotationBuilding": "Building", "unitPlannerStep3CenterRotationDrama": "Drama", "unitPlannerStep3CenterRotationLibrary": "Library", "unitPlannerStep3CenterRotationArt": "Art", "unitPlannerStep3CenterRotationBlocks": "Blocks", "unitPlannerStep3CenterRotationSensoryTable": "Sensory Table", "unitPlannerStep3CenterRotationMath": "Math", "unitPlannerStep3CenterRotationDramaticPlay": "Dramatic Play", "unitPlannerStep3CenterRotationLibraryListening": "Library & Listening", "unitPlannerStep3CenterRotationScienceEngineering": "Science & Engineering", "unitPlannerStep3CenterRotationWritingDrawing": "Writing & Drawing", "unitPlannerStep3CenterRotationPleaseSelectTheRotateCenters": "Please select the centers you want to rotate.", "unitPlannerStep3CenterRotationLargeGroupsAndSmallGroupsConfirmation": "Please confirm the activities for both large groups and small groups before proceeding to generate center ideas.", "unitPlannerStep3StationRotationLargeGroupsAndSmallGroupsConfirmation": "Please confirm the activities for the current week before proceeding to generate station ideas.", "unitPlannerStep3SelectCenters": "Select Centers", "unitPlannerStep3SelectStations": "Select Stations", "unitPlannerStep3PleaseSelectActivitiesCenters": "Please select the Centers that you would like to generate the activities. ", "unitPlannerStep3PleaseSelectActivitiesStations": "Please select the Stations that you would like to generate the activities. ", "unitPlannerStep3SelectAll": "Select All", "unitPlannerStep3Confirmation1": "Are you sure you want to regenerate all lessons?", "unitPlannerStep3Confirmation4": "We noticed that you haven't confirmed all lesson plan ideas for Week {week}. Would you like to confirm them all?", "unitPlannerStep3Confirmation5": "Are you sure you want to regenerate all center lessons?", "unitPlannerStep3Confirmation6": "Are you sure you want to delete this lesson?", "unitPlannerUpdateStandardsRegenerate": "Update and Regenerate", "unitPlannerEasilyModify": "Customize the standards and update your lesson plan to emphasize the areas of development you want to prioritize.", "unitPlannerMeasuresStandards2": "Standards", "unitPlannerRedesignGuide1": "Refine your lesson plan to better support your teaching objectives and elevate student learning outcomes.", "unitPlannerRedesignGuide2": "Later", "unitPlannerRedesignGuide3": "Try it now", "unitPlannerRedesignIdeaPlaceholder": "How would you enhance this lesson? Try introducing new materials, choosing a different book, or redesigning the lesson plan to enhance the learning experience.", "unitPlannerConfirmRegenerate": "Confirm and Regenerate", "unitPlannerLessonIdeas": "Lesson Plan Ideas", "unitPlannerLessonObjectives": "Objectives", "unitPlannerLessonMaterials": "Materials", "unitPlannerLessonDLLAndPhrases": "DLL Vocabulary and Phrases", "unitPlannerLessonDLLAndPhrasesPlaceholder": "List key vocabulary words and phrases to support learners.", "unitPlannerLessonImplementationSteps": "Implementation Steps", "unitPlannerLessonImplementationStepsPlaceholder": "Add clear, step-by-step instructions for implementation here.", "clrEditComponentPlaceholder": "Outline specific strategies, resources, and materials to support children from different cultural backgrounds, such as integrating specific cultural elements, multilingual labels and signs, etc.", "unitPlannerLessonUDL": "Universal Design for Differentiated Learning", "unitPlannerLessonForLearnersIEPs": "For Learners with IEPs/IFSPs", "unitPlannerLessonForEnglishLanguageLearners": "For English Language Learners", "unitPlannerLessonForDualLanguageLearners": "For Dual Language Learners", "unitPlannerLessonSupport": "Support", "unitPlannerLessonTypicalBehaviorsTips": "Typical Behaviors and Observation Tips", "unitPlannerLessonAtHomeActivities": "At-Home Activities", "unitPlannerLessonProceedPlan": "Proceed to Next Lesson Plan", "unitPlannerPreviewUnitPlan": "Preview Unit Plan", "unitPlannerEditUnitOverview": "Edit Unit Overview", "unitPlannerChildListChildName": "Child's Name", "unitPlannerChildListHispanic": "Hispanic/Latino", "unitPlannerChildListRace": "Race", "unitPlannerChildListHomeLanguage": "Home Language", "unitPlannerChildListSpecialEducationEligibility": "Special Education Eligibility", "unitPlannerChildListComments": "Comments", "unitPlannerChildListAdaptations": "Adaptations", "unitPlannerChildListIEPGoal": "IEP Goal", "unitPlannerChildListAction": "Action", "unitPlannerChildListTitle2": "To receive tailored guidance for IEP/IFSP learners, please complete their eligibility information.", "unitPlannerFeedback": "Provide additional feedback", "unitPlannerFeedbackPlaceholder": "Please leave your feedback here.", "unitPlannerFeedbackSubmit": "Submit", "unitPlannerAssignTeacherGroup": "Assign Teacher Groups", "unitPlannerAssignTeacherGroupTitle": "Please set the teacher groups for your current class. We will then create individualized groups for each teacher while adapting the lesson plans., specially tailored to better address the unique needs of IEP and ELD children. Additionally, the system will incorporate CLR practices to ensure an inclusive environment for all, regardless of their cultural backgrounds.", "unitPlannerAssignTeacherGroupAdd": "Add Teacher & Assign Groups", "unitPlannerAssignTeacherGroupSelectChild": "Select Children", "unitPlannerAssignTeacherGroupSetGroupSize": "Set Group Size Preferences", "unitPlannerAssignTeacherGroupSetGroupSizeTitle": "Define the preferred group size, and the system will generate groups accordingly for each teacher, enabling tailored support for each group.", "unitPlannerAssignTeacherGroupSetGroupSizeErrorTitle": "Invalid group size.", "unitPlannerAssignTeacherEmptyPage": "No teachers were assigned to this class.", "unitPlannerPersonalizePlan": "Personalize Your Lesson Plan", "unitPlannerPersonalizePlanTitle": "We specialize in supporting IEP and ELD children and offer culturally responsive practices for your lesson plan. Proceed to adapt for your class's unique needs?", "unitPlannerPersonalizePlanTotalChildren": "Total children: {count}", "unitPlannerPersonalizePlanClassroomDemographics": "Demographics", "unitPlannerPersonalizePlanEnableGroup": "Enable teacher-based groups for UDL", "unitPlannerPersonalizePlanConfirm": "Confirm and Start Adapting", "unitPlannerPersonalizePlanErrorTitle": "Since no information about children's race or place of origin is provided, and there are no children with IEPs, ELD needs, or in mixed-age groups, tailored UDL and CLR practices, as well as age-based differentiation, cannot be implemented.", "unitPlannerPersonalizePlanErrorTitle2": "Since no information about the children's race or place of origin is provided, and there are no children with IEPs or ELD needs, tailored UDL and CLR practices cannot be implemented.", "unitPlannerUDLError": "There are no IEP or ELD children in this group, so we cannot provide tailored UDL.", "unitPlannerUDLDragErrorTitle": "Each group must retain at least one child.", "unitPlannerUDLDragTitle": "Tips: Drag child's profile picture to rearrange groups.", "unitPlannerPlanItemAssignHomeLanguageHomeworkToFamilies": "Assign Home-language Homework to Families", "unitPlannerPlanItemManageClassroomDemographics": "Manage Classroom Demographics", "unitPlannerPlanItemNewSetting": "New Settings", "unitPlannerPlanItemNewSettingGuideContent1": "Easily manage children's attributes to customize each lesson plan to address your class's unique needs.", "unitPlannerPlanItemNewSettingGuideContent2": "Manage teacher groups here for more effective implementation of each lesson plan.", "unitPlannerPlanItemAdaptUDLAndCLRForMyClass": "Adapt UDL and CLR for My Class", "unitPlannerPlanItemAdaptUDLAndCLRForMyClassGuideContent": "Integrates UDL and CLR Strategies that adapt to your children's unique needs into every lesson plan, fostering an inclusive and diverse classroom environment.", "unitPlannerPlanItemAdaptUDLAndCLRForMyClassGuideAdapt": "Adapt", "unitPlannerCLRAndUDLGeneralInstructions": "General Instructions", "unitPlannerCLRAndUDLInclusiveLearningGroups": "Inclusive Learning Groups", "unitPlannerCLRAndUDLGroupInfo": "Classroom Child Info", "unitPlannerCLRAndUDLELDChildren": "ELD Children", "unitPlannerCLRAndUDLIEPChildren": "IEP Children", "unitPlannerCLRAndUDLIEPAndELDChildren": "IEP and ELD Children", "unitPlannerCLR": "Culturally and Linguistically Responsive Practice", "unitCoverCheckTip": "Please regenerate or upload the cover image here.", "unitPlannerRecommendationApplied": "Recommendation applied.", "typicalBehaviorsPSTip": "Through the <a href='https://www.learning-genie.com/wp-content/uploads/2024/09/DRDP-and-PTKLF_-Framework-Alignment-Guide.pdf' target='_blank' style='text-decoration: underline; color: #10B3B7;'>alignment of CA-PTKLF and DRDP frameworks</a>, weekly plans for DRDP classes will automatically convert PTKLF standards into DRDP measures, enabling seamless observation, rating, and report generation.", "measureMapTip": "Note: Not all PTKLF standards are mapped to DRDP measures.", "ptklfFoundations": "PTKLF Standards", "ptklfFoundationsTip": "With the alignment of CA-PTKLF and DRDP-PSC frameworks, activities based on CA-PTKLF will show the corresponding DRDP measures. Due to different mapping methods—Unit Planner maps PTKLF to DRDP, while Weekly Planner maps DRDP to PTKLF—the number of standards may vary.", "drdpStandards": "DRDP Measures", "drdpMeasureMapTip": "Since the class uses the DRDP framework aligned with CA-PTKLF, both DRDP and PTKLF standards are displayed in your Weekly planners. Due to different mapping methods—Unit Planner maps PTKLF to DRDP, while Weekly Planner maps DRDP to PTKLF—the number of standards may vary.", "measureMapGenerateTip": "Please click the regenerate button to generate Typical Behaviors and Observation Tips based on PTKLF standards mapped to DRDP measures.", "measureMapAddTip": "Please edit the lesson to add Typical Behaviors and Observation Tips based on DRDP measures.", "measureMapEmptyTip": "No DRDP measures are mapped to the PTKLF standards.", "unitPlanner": "Unit Planner", "unitPlannerRenegerateTip": "Please click the 'Regenerate' icon to proceed.", "adaptedLesson": "Adapted", "batchAdaptLessonPlan": "Batch Adapt Lesson Plans", "batchAdaptLessonPlanTip": "Batch adapt your weekly lesson plans to include UDL and CLR strategies personalized to your children's unique needs, fostering an inclusive and diverse classroom atmosphere.", "batchAdaptLesson2": "Your recent changes have not been saved. Would you like to save them before exiting?", "batchAdaptLesson3": "Adapted all lesson plans successfully.", "batchAdaptLesson4": "Edit Lesson Plans", "batchAdaptLesson5": "Please select the lesson plans you wish to adapt for a more inclusive learning experience that meets the unique needs of children in your classroom.", "batchAdaptLesson6": "Lesson Title", "batchAdaptLesson7": "Adaptation Status", "batchAdaptLesson8": "Please note that adaptations to lesson plans are still in progress. Exiting now will result in unsaved changes for any lessons that are still being adapted. However, any completed adaptations have already been saved.", "batchAdaptLesson9": "Exit Now", "batchAdaptLesson10": "Retry Adaptation", "batchAdaptLesson11": "It seems there was an issue adapting the lesson plan. Please retry to continue.", "batchAdaptLesson12": "Retry", "batchAdaptLesson13": "Please note that adaptations of center activities and lesson plans that consist solely of titles or links are currently not supported.", "batchAdaptLesson14": "Please add an activity to your weekly plan first.", "batchAdaptLesson15": "Weekdays", "batchAdaptLesson16": "Save All", "batchAdaptLesson17": "Please add both teachers and children to this class first.", "batchAdaptLesson19": "Adapted successfully!", "batchAdaptLesson20": "Save & Next", "batchAdaptLessonDialogTitle": "Edit Lesson Plan", "allLessonType": "All Lessons", "adaptedLessonType": "Adapted Lessons", "generalLessonType": "General Lessons", "nonbinary": "Nonbinary", "UnitCreateByMe": "Created by me", "UnitCreateByOthers": "Created by others", "UnitCreateByAnyone": "Created by anyone", "UnitSearchUnitName": "Search unit name", "unitPlannerWeeks": "weeks", "unitPlannerWeek": "week", "autoAdapteAgencyTemplate": "Auto adapt to my weekly planner template", "SingeLessonNotification": "{count} lesson plan has been successfully generated.", "MultipleLessonNotification": "{count} lesson plans have been successfully generated.", "unitNotification": "1 unit has been successfully generated.", "editOrContinueUnitPlannerButton": "Edit/Continue to Generate", "editOrContinueUnitPlannerTitle": "Enhance Your Unit Planner: Edit and Complete Now!", "editOrContinueUnitPlannerContent": "Click here to refine your planner or continue generating incomplete lesson plan details. Keep building your perfect unit planner now!", "gotIt": "Got it", "seamlesslyIntoWeeklyPlansTitle": "Seamlessly Integrate into Weekly Plans", "seamlesslyIntoWeeklyPlansContent": "With this unit now completed, you can seamlessly apply it to your weekly plans for immediate implementation. Effortlessly activate new strategies and lessons, and unlock the enhanced teaching experiences that await you!", "unitPlannerIsEditing": " is editing this unit planner.", "lessonsAreBeingAdapted": "Lesson plan details are still being adapted. Just a moment, please...", "unitRename": "<PERSON><PERSON>", "unitRenameInput": "Please enter a unit name first", "unitRenameInputWarn": "Please enter a unit name first.", "unitRenameSave": "Save", "unitRenameSuccess": "Saved successfully.", "unitOverview": "Unit Overview", "unitOverviewPlaceholder": "A brief summary of the unit, outlining the key themes, learning objectives, and overall purpose.", "unitTrajectoryPlaceholder": "A structured plan detailing the key learning experiences, instructional approaches, and progression of the unit.", "unitConceptsPlaceholder": "The central ideas and themes that students will explore and develop a deeper understanding of throughout the unit.", "unitGuidingQuestionsPlaceholder": "Open-ended questions designed to encourage critical thinking, inquiry, and meaningful discussion about the unit's topics.", "lessonAdapt": "Adapt", "adaptUDLAndCLRButton": "Adapt UDL and CLR", "adaptUDLAndCLRDesc": "Our innovative solution integrates UDL (Universal Design for Learning) and CLR (Culturally and Linguistically Responsive Practice) into your lesson plan, ensuring a welcoming, inclusive environment where every child feels valued and empowered to excel.", "generatingLesson": "Generating lesson plan details...", "generatingLessons": "Generating lesson plan details for all weeks...", "generatingLessons2": "This may take 2-3 minutes. You may continue to work on other tasks while your content is being generated.", "batchLessonFailed": "Some lesson plan details were not generated. Would you like to try regenerating them?", "batchLessonFailedRetry": "Regenerate", "noIEPOrELDNote": "Note", "noIEDOrELDClose": "Close", "batchGeneratedSuccessfully": "Batch generated successfully!", "unitDeleteWarning": "Are you sure you want to delete the <span style='font-weight: bold'> {name} </span> unit?", "pluginUnit5": "Which countries/regions are your students from?", "pluginUnit6": "What are the racial or ethnic backgrounds of your students?", "pluginUnit7": "What are the home languages of your students?", "pluginUnit8": "We specialize in supporting IEP/ELD children and offer culturally responsive practices for each lesson plan. Please select your unit to adapt and provide the following info. ", "eduprotocols1": "Our lessons now integrate with EduProtocols templates to boost teaching efficiency and foster collaborative, creative, and interactive learning experiences. ", "eduprotocols2": "* The system will automatically match the best templates for all regular activities (excluding station activities).", "eduprotocols3": "Generate without template", "eduprotocols4": "Apply template and generate", "eduprotocols5": "Please select your preferred EduProtocols template. ", "eduprotocols6": "All related content, including the lesson plan, UDL, CLR, Teaching Tips and more, will be regenerated according to the new template.", "eduprotocols9": "EduProtocols Template", "eduprotocols10": "EduProtocols template-based content generated successfully.", "eduprotocols11": "Student-ready Activities", "eduprotocols12": "Open Google Slides", "eduprotocols13": "Share to Classroom", "eduprotocols14": "Download PPTX", "eduprotocols15": "Select your preferred EduProtocols template here to boost your teaching efficiency and foster collaborative, creative, and interactive learning experiences. ", "eduprotocols16": "The EduProtocols template is only available for a single age group.", "eduprotocols17": "The EduProtocols template is not supported for age groups below Kindergarten.", "eduprotocols18": "Select Circles:", "eduprotocols19": "Select EduProtocols Template", "eduprotocols20": "More templates coming soon!", "eduprotocols21": "No book found. Please ensure the lesson contains a book to use the BookaKucha template.", "eduprotocols22": "This template is only for Grade 3 and above.", "eduprotocols23": "This lesson has no book. The template content cannot be updated.", "eduprotocols24": "Add New Template", "eduprotocols25": "Select a template and input your ideas to tailor it to your specific needs.", "eduprotocols26": "Select EduProtocols Template:", "eduprotocols27": "Personalize Your Template", "eduprotocols28": "New EduProtocols template", "eduprotocols29": "Are you sure you want to delete this EduProtocols template?", "eduprotocols30": "Delete", "eduprotocols31": "This template cannot be deleted because it is currently in use in the lesson.", "eduprotocols32": "Edit Template", "eduprotocols33": "Edit EduProtocols Template", "eduprotocols34": "You can customize the template content to better fit the lesson.", "eduprotocols35": "Confirm & Apply", "eduprotocols36": "Template updated successfully!", "eduprotocols37": "Easily customize template content to fit your ideas and enrich the lesson.", "eduprotocols38": "Add Now", "eduprotocols39": "Please keep at least one entry.", "eduprotocols40": "Book", "eduprotocols41": "Topic", "eduprotocols42": "Instruction", "eduprotocols43": "Enter the title of the book", "eduprotocols44": "Specify the main topic or theme", "eduprotocols45": "Provide specific instructions for the activity", "eduprotocols46": "Enter a central term or concept to focus on", "eduprotocols47": "Explore a specific aspect of the central term/concept", "eduprotocols48": "Quadrant {num}:", "eduprotocols49": "Enter a central concept", "eduprotocols50": "Content", "eduprotocols51": "Enter content that encourages exploration", "eduprotocols52": "Topic", "eduprotocols53": "Enter a topic word", "eduprotocols54": "Task", "eduprotocols55": "Enter task description", "eduprotocols56": "Circle", "eduprotocols57": "Prompt", "eduprotocols58": "Enter an opening prompt", "eduprotocols59": "Enter a central term", "eduprotocols60": "Enter a key concept", "eduprotocols61": "No book found for BookaKucha template, please add a book to continue.", "frayerModel": "<PERSON><PERSON><PERSON>", "frayerModelTitleDesc": "The Frayer Model is a four-quadrant graphic organizer that helps students explore, define, and analyze concepts or vocabulary for deeper understanding and active engagement.", "frayerModelDesc": "A 4-part organizer for mastering content and vocabulary.", "frayerModelPreviewTitle": "Frayer Model: Simplified Learning Tool", "frayerModelCustomPlaceholder": "Please describe how you would like to customize this template (e.g., based on the concept of 'Share', create content across 4 distinct dimensions to foster deeper understanding).", "sketchAndTell": "Sketch and Tell", "sketchAndTellTitleDesc": "The Sketch and Tell template is a simple, interactive tool that lets students creatively draw and quickly explain words or ideas, making learning more visual, engaging, and memorable.", "sketchAndTellDesc": "Use language and sketches to understand knowledge.", "sketchAndTellPreviewTitle": "Sketch and Tell: Visual Concept Tool", "sketchAndTellCustomPlaceholder": "Please describe how you would like to customize this template (e.g., generate 3 related concepts for 'Friend').", "sketchAndTellO": "Sketch and Tell-O", "sketchAndTellOTitleDesc": "The Sketch and Tell-O template allows students to map ideas in multiple circles, connecting them with labels and keywords to enhance understanding, critical thinking, and creativity.", "sketchAndTellODesc": "Slow your thinking down with a series of sketches.", "sketchAndTellOPreviewTitle": "Sketch and Tell-O: Circle-Based Visual Tool", "sketchAndTellOCustomPlaceholder": "Please describe how you would like to customize this template (e.g., generate a topic and a task based on the theme of 'Weather Pattern').", "bookaKucha": "BookaKucha", "bookaKuchaTitleDesc": "The BookaKucha is a creative book report tool inspired by PechaKucha, using minimal text and images to help summarize key ideas from books and foster concise communication and effective storytelling.", "bookaKuchaDesc": "An independent reading book report template kids will love!", "bookaKuchaPreviewTitle": "BookaKucha: A Book Report Tool", "bookaKuchaCustomPlaceholder": "Please describe how you would like to customize this template (e.g., please generate an open-ended topic for 'Charlotte's Web' to help students engage in deeper discussion).", "cyberSandwich": "Cyber Sandwich", "cyberSandwichTitleDesc": "The Cyber Sandwich is a collaborative activity that encourages students to compare and contrast topics through research, discussion, and summarization, promoting critical thinking, comprehension, and teamwork.", "cyberSandwichDesc": "A structured think-pair-share tool to deepen comprehension.", "cyberSandwichPreviewTitle": "Cyber Sandwich: Structured Think-Pair-Share", "thickSlide": "<PERSON><PERSON><PERSON>", "thickSlideTitleDesc": "The Thick Slide is a structured tool that helps students organize and present information through key facts, quotes, comparisons, and visuals, fostering critical thinking, clarity, and well-organized presentations.", "thickSlideDesc": "A report in a slide!", "thickSlidePreviewTitle": "Thick Slide: Idea Structuring Tool", "thinSlide": "Thin Slide", "thinSlideTitleDesc": "The Thin Slide template helps students research, define, and illustrate key terms with images and context sentences. This approach promotes clear communication and a deeper understanding. ", "thinSlideDesc": "A method for researching, rephrasing, and visually presenting key concepts.", "thinSlidePreviewTitle": "Thin Slide: Concept Reframing Tool", "thinSlideCustomPlaceholder": "Please describe how you would like to customize this template (e.g., generate 3 related terms for 'Environment').", "thinSlideV2": "Thin Slides Variations", "thinSlideV2TitleDesc": "The Thin Slides Variations template uses a few words and an image to convey ideas concisely and creatively. This approach promotes clear communication and allows for quick understanding of information.", "thinSlideV2Desc": "One word and one image: a powerful classwide sharing EduProtocol.", "thinSlideV2PreviewTitle": "Thin Slides Variations: Minimalist Idea Expression Tool", "thinSlideV2CustomPlaceholder": "Please describe how you would like to customize this template (e.g., generate template content based on the concept of 'Food').", "wickedHydra": "Wicked Hydra", "wickedHydraTitleDesc": "The Wicked Hydra is a questioning strategy that begins with thought-provoking content, enabling students to create a network of interconnected questions, which fosters deeper exploration, critical thinking, and a broader understanding without rushing to conclusions. ", "wickedHydraDesc": "Collaborative mind-map for deep questioning.", "wickedHydraPreviewTitle": "Wicked Hydra: Collaborative Deep Questioning Tool", "wickedHydraCustomPlaceholder": "Please describe how you would like to customize this template (e.g., generate a thought-provoking content related to 'Ocean Conservation').", "lessonSlide1": "Slides", "lessonSlide2": "View slides", "lessonSlide3": "Generate slides", "lessonSlide4": "Replace slides", "lessonSlide5": "Paste your Google Slides link here.", "lessonSlide6": "Upload slides", "lessonSlide7": "Supported file types: .pptx, .ppt", "lessonSlide8": "Please enter a valid Google Slides link.", "lessonSlide9": "Teacher Slides", "lessonSlide10": "Updated on ", "lessonSlide11": "lmporting your awesome presentation...", "lessonSlide12": "Uploading <strong>{num} new slides</strong> will overwrite your current slides and cannot be undone.", "lessonSlide13": "Confirm & Replace", "lessonSlide14": "Slides successfully replaced.", "lessonSlide15": "The teacher slides for <span style='font-weight: 700'>{slidesName}</span> has been successfully exported to your Google Drive.", "lessonSlide16": "Access denied. Please set your Google Slides link to 'Anyone with the link can view' and try again.", "lessonSlide17": "Streamline lesson preparation with clear, engaging visuals for effective teaching.", "lessonSlide18": "Please upload a .pptx or .ppt file.", "lessonSlide19": "Please upload a file under 20MB.", "lessonSlide20": "Teacher slides were generated successfully.", "lessonSlide21": "Your slides are still loading and will be ready soon.", "lessonSlide22": "Replace Image: You can search for an image or upload a new one.", "lessonSlide23": "Search an Image", "lessonSlide24": "Upload from Computer", "lessonSlide25": "Search", "lessonSlide26": "Select an image below or search with keywords.", "lessonSlide27": "Images you upload can not exceed 10MB in size.", "lessonSlide28": "Slideshow", "lessonSlide29": "Image search failed.", "lessonSlide30": "Failed to set image.", "unitPlannerEDI": "DEI Best Practices", "unitPlannerDEI2": "Diversity, Equity, Inclusion (DEI) Best Practices", "unitPlannerDEI3": "DEI best practices generated successfully!", "unitPlannerDEI4": "Your recent changes have not been saved. Would you like to save them before exiting?", "unitPlannerDEI5": "Are you sure you want to leave now? Changes you made will not be saved after confirming.", "unitPlannerDEI6": "For Teachers", "unitPlannerDEI7": "For Administrators", "unitPlannerDEI8": "Please fill in the DEI Best Practices content", "unitPlannerDEI9": "View", "generateDEIFailed": "Generation failed, please try again.", "adaptUnitPlanner2": "We specialize in supporting IEP/ELD children and provide culturally responsive practices in each lesson plan. Would you like to proceed with adapting for your children's unique needs?", "adaptUnitPlanner3": "Select Unit", "adaptUnitPlanner31": "Select Your Unit", "adaptUnitPlanner7": "Confirm and Start Adapting", "adaptUnitPlanner8": "The lesson plans have been successfully copied and adapted into a new unit.", "adaptUnitPlanner9": "It appears that Unit {unitName} has already been adapted for a different class. To proceed, we'll create a copy of the original content you've selected and adapt it for {groupName}. Would you like to continue with this adaptation?", "unitPlannerShowSources": "Show Resources", "unitPlannerHideSources": "Hide Resources", "unitPlannerSources": "Resources", "unitPlannerNotHasSources": "No resources are found in the text.", "unitPlannerRegenerateSuf": "Regenerated successfully.", "unitPlannerStep3CenterRotationOutdoor": "Outdoor", "unitPlannerStep3CenterRotationFineMotorArea": "Fine Motor Area", "unitPlannerStep3CenterRotationMusicMovement": "Music and Movement", "unitPlannerStep3CenterRotationLibraryQuietArea": "Library/Quiet Area", "unitPlannerStep3CenterRotationNatureScienceDiscoveryArea": "Nature Science/Discovery Area", "unitPlannerStep3CenterRotationSandWaterArea": "Sand and/or Water Area", "lessonImportAgeGroupTip": "Would you like to carry over the lesson plan details from the previous age group?", "unitPlannerAdaptedGuideTip": "Adapt Your Unit Plan for UDL and CLR", "unitPlannerCompletionCondition": "All completion status", "unitPlannerAdaptedCondition": "All adaptation status", "unitAdaptedGroup": "Adapted for: ", "unitPlannerAdapted": "Adapt Unit", "adaptUnitPlanner10": "The unit has been successfully adapted and added to the weekly plan for Class {groupName}", "adaptUnitPlanner11": "View Unit", "adaptUnitPlanner12": "View in Weekly Planner", "adaptUnitPlanner13": "Adapt Unit", "adaptUnitPlanner14": "Note: The class you selected differs from the class for which the selected unit has been adapted.", "adaptUnitPlanner15": "Note: The class you selected differs from the class for which the selected units have been adapted.", "adaptUnitPlanner16": "Note: The class you selected differs from the classes for which the selected units have been adapted.", "adaptUnitPlanner20": "No teacher is assigned to this class. Please assign a teacher to the class before adapting the unit.", "adaptUnitPlanner21": "🚀 Adapting lesson plans with class-specific UDL and CLR for all weeks...", "adaptUnitPlanner22": "Batch Adapt Lesson Plans", "adaptUnitPlanner23": "Proceed with Next", "adaptUnitPlanner24": "Save All", "adaptUnitPlanner25": "Adapted", "lessonQuiz1": "Formative Assessment", "LessonQuiz2": "Type", "LessonQuiz3": "<PERSON>'s Taxonomy", "LessonQuiz5": "Regenerate", "LessonQuiz6": "Add question", "LessonQuiz7": "Please enter your question here.", "LessonQuiz8": "Please enter your answer here.", "LessonQuiz9": "Answer Key", "LessonQuiz10": "Please select the assessment framework first.", "LessonQuiz11": "Delete", "LessonQuiz12": "Drag to Reorder", "lessonQuizQuestionType1": "Fill in the Blank", "lessonQuizQuestionType2": "Multiple Choice", "lessonQuizQuestionType3": "Matching", "lessonQuizQuestionType4": "True or False", "lessonQuizQuestionType5": "Checkboxes", "lessonQuizQuestionType6": "Sequencing", "lessonQuizQuestionType7": "Open-ended Question", "lessonQuizQuestionType8": "Short Answer", "lessonQuizQuestionType9": "Close Reading and Inferences", "lessonQuizQuestionType10": "Long Response", "lessonQuizQuestionType11": "Research & Reflect", "lessonQuizQuestionType12": "Essay Question", "lessonQuizQuestionType13": "Case Study", "lessonQuizQuestionType14": "Creative Corner", "lessonQuizLevel1": "Remember", "lessonQuizLevel1Desc": "Recall facts and basic concepts", "lessonQuizLevel2": "Understand", "lessonQuizLevel2Desc": "Grasp and explain the meaning of instructional materials", "lessonQuizLevel3": "Apply", "lessonQuizLevel3Desc": "Use existing knowledge in new contexts", "lessonQuizLevel4": "Analyze", "lessonQuizLevel4Desc": "Explore relationships, causes, and connections.", "lessonQuizLevel5": "Evaluate", "lessonQuizLevel5Desc": "Make decisions after evaluating the information", "lessonQuizLevel6": "Create", "lessonQuizLevel6Desc": "Use existing information to make something new", "lessonQuizLevelBloom": "<PERSON>'s Taxonomy", "lessonQuizLevelBloomDesc": "<PERSON>'s Taxonomy is a hierarchical model used for categorizing educational learning objectives by levels of complexity and specificity. It is divided into six cognitive levels, which educators often use to design quizzes that promote students' higher-order thinking skills.", "lessonQuizLevelDOKAbbr": "DOK", "lessonQuizLevelDOK": "DOK (Depth of Knowledge)", "lessonQuizLevelDOKDesc": "Depth of Knowledge (DOK) is a framework that categorizes educational tasks by cognitive complexity, from simple fact recall (Level 1) to extended strategic thinking (Level 4). It helps educators align curriculum and assessments with cognitive demands to enhance student understanding and higher-order thinking skills.", "lessonQuizLevelDOK1": "DOK 1", "lessonQuizLevelDOK1Desc": "Recall and Reproduction", "lessonQuizLevelDOK2": "DOK 2", "lessonQuizLevelDOK2Desc": "Skills and Concepts", "lessonQuizLevelDOK3": "DOK 3", "lessonQuizLevelDOK3Desc": "Strategic Thinking", "lessonQuizLevelDOK4": "DOK 4", "lessonQuizLevelDOK4Desc": "Extended Thinking", "lessonQuizLevelStandsChangeTip": "Are you sure you want to switch the cognitive model? Once switched, all previously generated questions will be cleared.", "selectFileToDownload": "Download Formative Assessment", "withAnswerKey": "Include answer key", "withoutAnswerKey": "Exclude answer key", "atLeastOneOption": "Please select an option to download.", "quizDetails": "Details", "confirmAndRegenerate": "Confirm & Regenerate ", "curriculumPromptHistory": "Prompt History", "curriculumPromptHistoryEmptyTip": "No saved prompts. Start by creating your first one!", "curriculumPromptHistoryGuideTip": "Easily reuse previous prompts! Click on \"Prompt History\" to view and apply past prompts for faster unit planner creation.", "curriculumPromptHistoryTip": "Access your prompts from the last 1 year and get started right away.", "curriculumPromptHistoryDeleteTip": "Are you sure you want to delete this prompt?", "curriculumPromptHistoryUse": "Use", "updateAndRegenerate": "Update and Regenerate", "weekLength": "Week Length", "enhanceUnit": "Enhance Unit", "unitPlannerEnhanceDescription": "Refine your lesson plan to better support your teaching objectives and elevate student learning outcomes.", "enhanceIdeas": "Enhancement Ideas", "lessonConfirmTip": "Ready to batch generate lesson plans for all weeks? Please confirm to proceed.", "regenerateUnitTip": "Enhance the unit foundations by sharing your ideas, ensuring they are tailored to your teaching goals.", "regenerateUnitPlaceholder": "Please describe how you would like to enhance this unit.", "regenerateLessonPlanTip": "Update and regenerate your lesson plan to emphasize the areas of development you want to prioritize.", "regenerateLessonPlanPlaceholder": "Please describe how you would adapt this lesson plan.", "importAgeGroupInfoNotification": "Carried over successfully!", "repeatAdaptClassTips": "{selectedAdaptedPlansLabel} has already been adapted for {groupName}. Would you like to overwrite the existing content adapted for {groupName}, or create a new unit?", "repeatAdaptClassTips2": "{selectedAdaptedPlansLabel} has already been adapted based on your provided children info. Would you like to overwrite the existing adapted content or create a new unit with the adaptations?", "unitPlannerToWeekLyPlan": "Add unit to weekly plan", "unitPlannerAdaptDialogTips": "Only completed units that have not yet been adapted are displayed here.", "unitPlannerDialogCreateUnit": "Create New Unit", "unitPlannerGoal": "Overwrite Existing", "unitAdaptedActivities": "Adapted", "actionPlanSaveTipInfo": "You have unsaved changes in your Action Plan. Would you like to save them before exiting?", "unitDescriptionAssistant": "Description Assistant", "unitDescriptionGuideTitle": "Personalize Your Unit", "unitDescriptionGuideDescription1": "Write a concise description of the unit's main idea.", "unitDescriptionGuideDescription2": "List the anchor text you plan to use throughout the unit. ", "unitDescriptionGuideDescription3": "List all books you plan to introduce in this unit.", "unitDescriptionGuideDescription3Tip": "Please ensure the books you provide actually exist, or we will suggest a similar alternative.", "unitDescriptionGuideDescription4": "Describe the environment settings you hope this unit should be conducted.", "unitDescriptionGuideDescription5": "Outline the students' preferences for the design and development of this unit.", "unitDescriptionGuidePlaceholder1": "e.g. \"Students will explore the process of pollination and how it helps plants make seeds and grow.\"", "unitDescriptionGuidePlaceholder2": "e.g. The Great Gatsby by <PERSON><PERSON>\nURL: https://example.com", "unitDescriptionGuidePlaceholder3": "e.g. \"Flowers Are Calling by <PERSON>, <PERSON> Bees a Chance by <PERSON>\"", "unitDescriptionGuidePlaceholder4": "e.g. \"Classroom with a flower exploration station, and outdoor time in the garden to observe bees and flowers.\"", "unitDescriptionGuidePlaceholder5": "e.g. \"Students enjoy nature walks, role-playing as pollinators, and using art supplies to create flowers and bees.\"", "unitDescriptionGuideConfirm": "Confirm", "unitDescriptionGuideCancel": "Cancel", "unitDescriptionGuideTitle1": "Main idea of this unit", "unitDescriptionGuideTitle2": "Anchor text", "unitDescriptionGuideTitle3": "Books children will learn in this unit", "unitDescriptionGuideTitle4": "List of the environment settings", "unitDescriptionGuideTitle5": "Students' preferences", "dll1": "Show More", "dll2": "Show Less", "dll3": "View DLL Activities", "dll4": "Add Key Vocabularies", "dll5": "Please input a description.", "dll6": "Edit Key Vocabularies", "dll7": "Description", "dll8": "Are you sure you want to delete this vocabulary?", "planPleaseEnter": "Enter Description", "enterActivity": "Add Activity", "doPlan1": "An entire row (activity)", "doPlan2": "An entire row (text)", "doPlan3": "Row with daily cells (text)", "doPlan4": "Row with daily cells (activity)", "doPlan5": "Please drag lesson plans only to designated activity rows, not text-only rows.", "doPlan6": "Place at most one text cell.", "planDeleteSuccess": "Delete successfully!", "curriculumName": "Curriculum Name", "curriculumUnitSave": "Save", "curriculumActivities": "Total Lessons", "curriculumUnitSelectMeasure": "Please select standards", "curriculumUnitSelectSubject": "Please select subject/domains", "switchAgencyTip": "Are you sure you want to switch from {originalAgencyName} to {replacedAgencyName}? After switching, you can view and manage data of {replacedAgencyName}.", "onlyMainAgencyCanSetting": "You don't have permission to edit this agency's settings. If changes are needed, please contact the agency's admin.", "createdUserLabelAll": "Created by Anyone", "createdUserLabelMe": "Created by <PERSON>", "createdUserLabelOthers": "Created by Others", "weeklyPlanStatusLabelAll": "All Status", "weeklyPlanStatusLabelDraft": "Draft", "weeklyPlanStatusLabelApproved": "Approved", "weeklyPlanStatusLabelRevise": "Revise", "weeklyPlanStatusLabelPending": "Pending", "currentlyLanguageTip": "Currently in ", "lessonDetailTranslateFailed": "Translation failed! Please try again.", "childOrigin": "Child's place of origin", "palceOrigin": "Place of Origin", "languageSwitchOrigin": "Switch to original language", "continue": "Continue", "clrAdaptedUpdate0": "No teacher has been assigned to this class. Please assign a teacher before adapting the lesson plan.", "Share": "share", "unitPlannerStep1FrameworkWarning": "Are you sure you want to switch to another framework?", "repeatEntryTip": "This entry has already been submitted. You cannot submit it again.", "limitOneMagicPublish": "You can only submit one entry per account.", "weekPlanLessonNotSave": "Your recent changes have not been saved. Would you like to save them before exiting?", "weekPlanAiOnline": "Al Lesson Assistant Is Online!", "weekPlanAiAssistantToGenerate": "You can use the Al Lesson Assistant to generate the lesson you want with one click when adding activity.", "applyNow": "Apply Now", "placeSelectValue": "Please select a value", "SFTPSelectOUSDTip": "This option applies only to exporting data for children using OUSD TK Standards and DRDP PSF.", "week1": "Week 1", "week2": "Week 2", "week3": "Week 3", "week4": "Week 4", "week5": "Week 5", "week6": "Week 6", "week7": "Week 7", "week8": "Week 8", "week9": "Week 9", "week10": "Week 10", "mixAgeGroup1": "Mixed-age Differentiations", "mixAgeGroup2": "Mixed-age Differentiation Settings", "mixAgeGroup3": "Adapt teaching activities to the cognitive levels of different age groups, making mixed-age teaching easier for teachers to manage.", "mixAgeGroup4": "Later", "mixAgeGroup5": "Setup now", "mixAgeGroup6": "Automatically generate age-specific teaching tips based on classroom demographics when adapting lesson plans, helping teachers manage mixed-age classrooms and meet personalized needs more effectively.", "mixAgeGroup7": "Enable this feature to automatically generate specific instructions for younger age groups based on your classroom demographics when adapting lesson plans, helping teachers manage mixed-age classrooms and meet personalized needs more effectively.", "mixAgeGroup8": "Enable mixed-age differentiation", "mixAgeGroup9": "Note: Differentiated instructions will only be available if the following mixed-age groups are present in the class.", "mixAgeGroup10": "Adapted Lesson Plan", "mixAgeGroup11": "For adaptation of TK (4-5) lessons", "mixAgeGroup12": "For adaptation of PS/PK (3-4) lessons", "mixAgeGroup13": "Close", "mixAgeGroup14": "Mixed-age differentiation for the adapted lesson plan is disabled.", "mixAgeGroup15": "Mixed-age differentiation for the adapted lesson plan is enabled.", "mixAgeGroup16": "Disable this section via “Mixed-age Differentiation Settings” in the top-right Settings button of the Unit Planner.", "mixAgeGroup17": "Disable this section via “Mixed-age Differentiation Settings” in the top-right Settings button of the Edit Weekly Planner.", "mixAgeGroup18": "Describe how activities can be adapted to meet the needs of mixed-age children.", "mixAgeGroup19": "Provide clear, specific, and actionable instructions to adapt activities for mixed-age children.", "mixAgeGroup20": "Show IEP and ELD children only", "assessmentDomainMeasureList": "Assessment Standards List", "collectShowCoreMeasureOnly": "Show key measures only", "subjectCount": "Subject: {subjectCount}", "measureCount": "Standard: {measureCount}", "collectMeasureShowAll": "Show all", "collectMeasureHide": "<PERSON>de", "collectUnit": "Unit", "downloadIncludes": "This includes the unit foundations, weekly planners, all lesson plans, and center activities.", "downloadIncludes2": "This includes the unit foundations, weekly planners, all lesson plans (including lecture slides), station activities, and EduProtocols-based student activities.", "downloadIncludes3": "This includes the unit foundations, weekly planners, all lesson plans (including lecture slides), and EduProtocols-based student activities.", "downloadIncludes4": "This includes the unit foundations, weekly planners, all lesson plans (including lecture slides), and station activities.", "downloadIncludes5": "This includes the unit foundations, weekly planners, and all lesson plans (including lecture slides).", "downloadClosing": "Closing this window will not interrupt the download process in progress.", "downloadReceive": "* You'll receive an email with the download link.", "downloadBeing": "The unit “{unitName}” is being generated.", "downloadSuccessfully": "The unit “{unitName}” has been generated successfully.", "downloadPDF": "Downloading PDF", "downloadWord": "Downloading Word", "downloadGoogleDrive": "Exporting to Google Drive", "downloadSuccessfullyGoogle": "Your unit planner “{unitName}” has been successfully exported to Google Drive.", "downloadViewGoogleDrive": "View in Google Drive", "downloadViewWord": "Your unit planner “{unitName}” has been successfully generated.", "openInNewTab": "Open in new tab", "eduProtocolsViewInGoogle": "This EduProtocols Template: <span style='font-weight: 700'>{googleSlidesName}</span> has been successfully exported to your Google Drive.", "scienceOfReading1": "Science of Reading Activities", "scienceOfReading2": "Activity Title", "scienceOfReading3": "Please enter a title", "scienceOfReading4": "Foundational Literacy Standards", "scienceOfReading5": "Please select", "scienceOfReading6": "Describe your SOR activity to foster children's early foundational literacy skills.", "scienceOfReading7": "Please complete this section of the SOR Module.", "domainMappingMap2": "With the alignment of {abbr} and DRDP frameworks, activities based on {abbr} will show the corresponding DRDP measures. Due to different mapping methods—Unit Planner maps {abbr} to DRDP, while Weekly Planner maps DRDP to {abbr}—the number of standards may vary.", "domainMappingMap6": "No DRDP measures are mapped to the {abbr} standards.", "helpMeFindMyStateStandards": "Help me find my state standards", "helpMeFindMyStateStandardsTip": "If the state standards recently being updated, please let us know and we will update it asap.", "StateStandardsRequest": "State Standards Request", "StateStandardsRequestDialogTip": "Please elaborate on your state standards request.", "attributes": "Attributes", "expectations": "Expectations", "curriculumUnitSelectPortrait": "Please select", "lessonPortraitTitle": "Portrait of a Graduate", "lessonPortraitTip": "The {templateName} template also contributes to student growth, with a focus on: ", "lessonPortraitAttr": "Attributes", "lessonPortraitAttrValue": "Teaching Tips", "teachingTips": "Teaching Tips", "notGenerated": "Click 'Generate' below to create the expectations for this grade.", "pleaseExpectations": "Please complete the attributes or expectations.", "enhancementIdeas": "Enhancement Ideas", "createLessonBtn": "Create/Adapt Lesson", "createLesson": "C<PERSON> <PERSON><PERSON>", "adaptLesson": "<PERSON><PERSON><PERSON>", "activityDescriptionPlaceholder1": "What's your lesson about? Enter a topic or brief description to get started.", "activityDescriptionPlaceholder2": "Paste your existing activity or lesson plan here for adaptation.", "adaptationIdeasPlaceholder": "How would you like to adapt this lesson?", "filters": "Filters", "lessonAdaptationPromptHistory": "Lesson Adaptation Prompt History", "lessonCreationPromptHistory": "Lesson Creation Prompt History", "saving": "Saving...", "generating": "Generating...", "lastSaved": "Last saved at", "lessonVersionHistory": "Version History", "editPrompt": "Edit Prompt", "enhanceLesson": "<PERSON><PERSON><PERSON>", "enhanceLessonPlanTip": "Refine your lesson plan to better support your teaching objectives and elevate student learning outcomes.", "enhanceIdeasPlaceholder": "How would you enhance this lesson? Try introducing new materials, choosing a different book, or redesigning the lesson plan to enhance the learning experience.", "lessonPlanHistory": "Lesson Plan History", "lessonCurrentVersion": "Current version", "lessonRestore": "Rest<PERSON>", "noLessonHistory": "No version history found!", "noMoreLessonHistory": "No more versions", "lessonRestoreSuccess": "Version restored successfully.", "lessonRestoreDesc": "Your current lesson will revert to the version from", "lessonRestoreDialogTitle": "Restore this version?", "lessonRestoreLoadingText": "Restoring version, please wait...", "lessonWelcomeTitle": "AI-Powered Lesson Planning", "lessonWelcomeDescription": "Create or adapt smarter, future-ready lesson plans with magic.", "PersonalizationAdaptation": "Personalization & Adaptation", "pleaseProceeding": "Please enter your Portrait of a Graduate before proceeding.", "leastResoure": "At least one resource must be enabled.", "saveResourceSettingsFailed": "Failed to save resource settings. Please try again.", "getResoureSettingsFailed": "Failed to load resource settings. Please try again.", "regenerate": "Regenerate", "suggestNewResource": "Suggest a new resource", "manageResourcesDescription": "Choose which websites you'd like to prioritize when searching for lesson resources.", "manageResources": "Manage Resources", "downloadCSV": "Download CSV", "unitPlannerExemplarsTip": "Get started with ready-to-use exemplars here.", "exemplarToastMessage": "Exemplar {index} of {total} applied. <PERSON>lick again to switch to the next one.", "viewMyLessons": "View My Lessons", "viewLesson": "View Lesson", "unitPlannerWelcomeTipForControl": "Start your first unit now and unlock the power of smarter, more efficient planning.", "unitPlannerWelcomeTipForTest": "Create an entire unit in 8 minutes—simple, fast, and ready to go.", "downloadDialogMessage": "To download your unit, please finish the remaining steps. Don't worry—it only takes a few minutes to complete!", "generateNow": "Generate Now", "lessonsTemp": "lessons", "lessonTemp": "lesson", "adaptLessonDetailTip": "Our innovative solution integrates UDL (Universal Design for Learning) and CLR (Culturally and Linguistically Responsive Practice) into your lesson plan, ensuring a welcoming, inclusive environment where every child feels valued and empowered to excel.", "export": "Export", "exportAsPDF": "Export as PDF", "downloadAll": "Download all", "downloadAllTip": "All quizzes across lessons are available in .docx and .xlsx formats.", "downloadCurrent": "Download current", "fullDownloadResources": "Full Unit Kit (Plans & Resources)", "teachingResources": "Teaching Resources", "classroomSetup": "Classroom Setup", "classroomTypeInPerson": "In-person", "classroomTypeVirtual": "Virtual", "classroomSetupInPerson": "In-person Class", "classroomSetupVirtual": "Virtual Class", "classroomSetupTip": "Set the classroom type to match your teaching scenario: use In-person Class for onsite teaching and Virtual Class for online instruction.", "keyVocabulariesDownload": "Key Vocabularies (.csv)", "formativeAssessmentDownload": "Formative Assessment (.docx, .xlsx)", "adaptSelect": "Select Your Enhancement Mode", "adaptSelectTip": "Let AI enrich or redesign your unit based on your goals.", "adaptSelectLightAdapt": "Light Adapt (Batch Add-On)", "adaptSelectLightAdaptTip": "Keep your existing content and enrich all lessons at once with AI-powered instructional enhancements.", "adaptSelectDeepRedesign": "Deep Redesign", "adaptSelectDeepRedesignTip": "Fully rethink and rebuild your unit’s structure, content, and standards with AI.", "adaptSelectStartNow": "Start Now", "adaptSelectLightAdaptGroup": "Choose the enhancements you want to apply to this unit:", "adaptSelectPog": "Portrait of Graduate (PoG)", "adaptSelectPogDes": "Embed your district's Portrait of a Graduate into lesson goals and activities.", "adaptSelectTeacherGuide": "Teacher Guide (Job-Embedded Support)", "adaptSelectTeacherGuideDes": "Step-by-step guidance to help teachers implement lessons confidently.", "adaptSelectTeacherGuideTip": "This guide provides practical instructional support, including strategies for:", "adaptSelectTeacherGuideTip1": "Universal Design for Differentiated Learning (UDL)", "adaptSelectTeacherGuideTip2": "Culturally and Linguistically Responsive Practice (CLR)", "adaptSelectTeacherGuideTip3": "Teaching Tps for Standards", "adaptSelectTeacherGuideTip4": "Typical Behaviors and Observation Tips", "adaptSelectAssessment": "Standards-Aligned Assessment", "adaptSelectAssessmentDes": "Auto-generated quizzes mapped to your selected standards.", "adaptSelectEduProtocols": "EduProtocols Templates", "adaptSelectEduProtocolsDes": "Ready-to-use student activities that boost engagement, creativity, and participation.", "adaptSelectSlides": "Teacher Slides", "adaptSelectSlidesDes": "Ready-to-teach slides tailored to your unit's lessons.", "adaptSelectPairedTexts": "Paired Texts", "adaptSelectPairedTextsDes": "Thematically aligned, high-quality texts that expand student thinking.", "adaptSelectResourceUpgrade": "Resource Upgrade", "adaptSelectResourceUpgradeDes": "Replace with latest, best-fit resources.", "adaptSelectStartNowTip": "Please select an enhancement mode before continuing.", "adaptSelectCreatLimit": "You've reached your unit creation limit.", "adaptSelectLightAdaptLeastOne": "To get started, please choose at least one enhancement from the options below", "adaptSelectPogLeastOne": "To apply the 'Portrait of a Graduate' enhancement, please select at least one attribute.", "adaptSelectUnLightAdapt": "It looks like your uploaded document does not contain any lessons. The Light Adapt feature requires existing lessons to apply enhancements. To proceed, please select <strong>Deep Redesign</strong> — we’ll help you rethink or rebuild your unit and lessons using AI.", "adaptSelectUnLightAdaptBtn": "Continue with Deep Redesign", "studentVoice": "Student Voice", "unitPlannerDomainsNotGoodFit": "The selected subjects/domains may not be a good fit for this unit. Please choose different ones to proceed.", "unitPlannerDomainNotGoodFit": "The selected subject/domain may not be a good fit for this unit. Please choose a different one to proceed.", "unitPlannerMeasuresNotGoodFit": "Your selected standards (measures) may not be a good fit for this unit. Please choose different ones to proceed.", "recommendedForRemoval": "Recommended for Removal:", "unitPlannerStandardsDetected": "Standards Misalignment Detected", "applyAndGenerate": "Accept recommendations and proceed", "skipAndGenerate": "Ignore and continue anyway", "unitPlannerMeasuresNotAlign": "Some of the selected standards (measures) might not fully align with your current unit topic. We recommend removing these standards to help your unit stay focused and aligned.", "adaptImportFeedbackTitle": "<PERSON>", "adaptImportFeedbackTip": "We’d love your thoughts. If something didn’t look right or could be better, let us know!", "adaptImportFeedbackSelect": "Please select the type of feedback", "adaptImportFeedbackSelect1": "Something is missing", "adaptImportFeedbackSelect2": "The extracted info is incorrect", "adaptImportFeedbackSelect3": "Suggest a new feature", "adaptImportFeedbackSelect4": "Bugs", "adaptImportFeedbackSelect5": "Other", "adaptImportFeedbackInput": "Please provide details of your feedback. Describe the issue or idea…", "adaptImportFeedbackSuccess": "Thanks for your feedback! We’re reviewing it to improve your experience.", "selectGradeFirst": "Select a Grade first", "selectStateStandardsFirst": "Select State Standards first", "exportAsWord": "Export as Word", "validURLError": "Please enter a valid URL.", "pleaseUploadFile": "Please upload a file.", "uploadFileMaxNumberFour": "You can upload a maximum of 1 file.", "titleCannotExceed200Characters": " Title: 200-character limit.", "uploadFileTip": "Please upload a PDF, Word, Excel, or PowerPoint file.", "uploadFileExceedLimit": "Sorry, your upload file exceeds 10MB limit.", "restoreResourceVersion": "Restore to this version? This will overwrite your current version.", "resourceConfirm": "Confirm", "resourceCancel": "Cancel", "resourceEditGuided": " Easily add links or documents, and edit or remove existing resources to perfectly tailor them to your lesson needs. Streamline teaching, personalize learning!", "editResourceButton": "Edit Resources", "addResourceButton": "Add Resources", "resourceHistory": "History", "currentResources": "Current Resources", "viewMoreResources": "View more resources", "hideResources": "Hide resources", "uploadFailed": "Upload failed. Please try again.", "resourceAddedSuccessfully": "Added successfully!", "resourceDeletedSuccessfully": "Deleted successfully!", "resourceURL": "Resource URL", "resourceAdded": "Add", "resourceTitle": "Resource Title", "resourceTitlePlaceholder": "Enter resource title", "resourceType": "Resource Type", "URL": "URL", "File": "File", "resourceURLPlaceholder": "Add your own resource URL", "resourceAddCancel": "Cancel", "browseFiles": "Browse Files", "dropFilesHere": "Drop files here, or click \"Browse Files\" to upload. (Max: 10MB)", "resourceGuidedTitle": "Integrate Your Resources Seamlessly!", "editResourceNow": "Edit resources now", "resourceDeleteConfirm": "Are you sure to delete this resource?", "resourceDeleteConfirmTitle": "Confirmation", "resourceHistoryVersion": "Resources History", "versionHistory": "Version History", "restoreResource": "Rest<PERSON>", "removedSuccessfully": "File removed successfully.", "fromUnit": "From Unit:", "fromUnitSyncUnitPlanTip": "Edits to this lesson will sync with your Unit plan.", "resourceUpdatedSuccessfully": "Updated successfully!", "fullLessonExport": "Full Lesson Kit (Plan & Built-in Resources)", "downloadUnsuccessful": "Download unsuccessful. Please try again.", "importUnitExemplarTryToolTip": "Quick start with an example unit. No upload required.", "importUnitTitle": "Adapt Existing Unit", "importUnitTitleDesc": "Import your existing unit to get started.", "importUnitGoogle": "Google Docs URL", "importUnitGoogleDesc": "(Make sure the link sharing is set to \"Anyone with the link can view.\")", "importUnitGoogleUrlError": "Invalid URL. Please paste a Google Docs link.", "importUnitUploadTitle1": "Click to select or drag & drop your file", "importUnitUploadTitle2": "Supported formats: .pdf, .docx, .doc.", "importUnitUploadTitle3": "Max file size: 20MB", "importUnitLoadingTitle1": "Identifying unit title and overview...", "importUnitLoadingTitle2": "Outlining weekly topics...", "importUnitLoadingTitle3": "Extracting learning objectives and resources...", "importUnitLoadingTitle4": "Locating referenced resources...", "importUnitDrive": "Google Drive", "importUnitStandTitle": "Confirm Key Unit Information", "importUnitStandDesc": "Before we analyze your unit, please confirm the following key information to ensure accurate results.", "importUnitStandGrade": "Select the grade level from your original unit. Adjustments are supported.", "importUnitStandSubjectLimit": "Select up to 3 subjects for this unit. Interdisciplinary learning is supported.", "importUnitStandConfirm": "Confirm & Continue", "importUnitVarUrlAccess": "Link isn’t accessible. Set sharing to “Anyone with the link can view.”", "importUnitVarUrlType": "Unsupported file format. Please upload a .pdf, .docx, .doc file.", "importUnitVarUrlSize": "File exceeds the 20 MB size limit. Please use a smaller file.", "importUnitTwoFile": "Please use only one upload method.", "importUnitOcr1": "Invalid unit plan. Please check the content and try again.", "importUnitOcr2": "We ran into a problem processing your file. Please try again.", "importUnitOcr3": "We couldn't extract text from this document. Try another one.", "importUnitResultSuccess": "Successfully extracted! Please review the AI-detected unit details carefully before proceeding.", "importResultSuccessExtracted": "Successfully extracted! Please review the AI-detected unit details carefully before proceeding.", "importResultPleaseInputUnitTitle": "Please input unit title", "importResultStandardsAdjust": "Standards (Adjust to your state standards)", "importResultSpecifiedStandards": "Specified Standards", "importResultSelectStandardsManually": "Select standards manually or use auto-suggest", "importResultUnitBigIdea": "Unit Big Idea (Description Summary)", "importResultUnitBigIdeaPlaceholder": "A brief summary of the unit, outlining the key themes, learning objectives, and overall purpose.", "importResultUnitObjective": "Unit Objective", "importResultUnitObjectivePlaceholder": "A structured plan detailing the key learning experiences, instructional approaches, and progression of the unit.", "importResultWeeklyDescriptions": "Weekly Descriptions", "importResultWeeklyTheme": "Weekly Theme", "importResultWeeklyOverview": "Weekly Overview", "importResultNoWeeklyInfo": "No weekly information found.", "importResultLessonPlanName": "Lesson Plan Name", "importResultPleaseInputLessonName": "Please input lesson name", "importResultLessonObjective": "Lesson Objective", "importResultPleaseInputLessonObjective": "Please input lesson objective", "importResultLessonMaterials": "Lesson Materials", "importResultPleaseInputLessonMaterials": "Please input lesson materials", "importResultKeyVocabularyWords": "Key Vocabulary Words", "importResultPleaseInputKeyVocabulary": "Please input key vocabulary words", "importResultImplementationSteps": "Implementation Steps", "importResultPleaseInputImplementationSteps": "Please input implementation steps", "importResultBooksLinksAndMaterials": "Books Links and Student Materials", "importResultOriginalMaterialsPreserved": "Your original materials will be safely preserved.", "importResultNoReferenceLinks": "Your uploaded file contains no reference links.", "importResultStandardsMismatchFound": "A standards mismatch was found. The standards in the document are different from your selected framework", "importResultPleaseReviewAndSelect": "Please review and select the standards that best align with this unit.", "importResultApplyMyFramework": "Apply my selected framework", "importResultKeepDetectedFramework": "Keep detected framework", "importResultSelectUpTo3Subjects": "Select up to 3 subjects for this unit. Interdisciplinary learning is supported.", "importResultSubjectNotFit": "The selected subject/domain may not be a good fit for this unit, Please choose a different one to proceed.", "importResultUnknownFramework": "Unknown Framework", "numberMaxMeasure": "You can select up to {max} standards.", "importUnitAdaptDialogTitle": "✨ New! Adapt Existing Units with AI Magic", "importUnitAdaptDialogDesc": "Got a unit plan in a document? Let Curriculum Genie instantly upgrade it into a dynamic, ready-to-teach experience.", "importUnitAdaptDialogBtn": "👉 Get Started"}