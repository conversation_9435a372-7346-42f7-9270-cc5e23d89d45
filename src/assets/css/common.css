/*
 reset css and  add create class css
*/
/* 全局颜色变量 */
:root {
  --main-bg-color: brown;
  --color-primary: #10b3b7;
  --color-primary-light: #ddf2f3;
  --color-primary-opacity: #10b3b799;
  --color-success: #67c23a;
  --color-success-light: #96c13d;
  --color-success-opacity: #67c23a99;
  --color-warning: #e6a23c;
  --color-warning-dark: #ff7f41;
  --color-warning-opacity: #e6a23c99;
  --color-danger: #f56c6c;
  --color-danger-opacity: #f56c6c99;
  --color-info: #909399;
  --color-info-opacity: #90939999;
  /* --color-text-primary: #323338; */
  --color-text-primary: #111c1c;
  --color-text-secondary: #323338;
  --color-text-disabled: #67687940;
  --color-text-placeholder: #676879;
  --color-text-record: #606266;
  --color-text-placeholder-opacity: #99999999;
  --color-border: #dcdfe6;
  --color-border-opacity: #dcdfe699;
  --color-white: #ffffff;
  --color-gray: #e6e9f1;
  --color-gray-opacity: #e6e9f199;
  --color-gray-white: #ebeef5;
  --color-gray-white-opacity: #ebeef599;
  --color-page-background-white: #f5f6f8;
  --color-table-header-background: #fafafa;
  --color-step-divider: #c5c7d0;
  --color-iep: #ff7900;
  --color-eld: #002efe;
  --color-ai-assistant: #878BF9;
  --color-table-background: #f7f8fb;
  --color-inner-dashed-border: #c0c4cc;
  --color-ai-assistant-opacity: #878BF933;
  --color-blue-light: #c7e5f6;
  --color-border-lighter: #F4FBFF;
  --color-gray-info-background: #f7f7f7;
}
html {
  font-size: 16px;
}

/*reset some css*/
ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}

.reset-ul {
  margin: 0;
  padding: 0;
}

.reset-ul > li,
.remove-li-default {
  list-style: none;
}

a {
  outline: none !important;
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 20px;
}

.final-color {
  color: #337ab7 !important;
}

p {
  padding: 0;
  margin: 0;
}

.text-info {
  color: #31708f !important;
}

.set-break-word,
.el-dialog__body {
  word-break: break-word;
}

/* font size */

.font-size-12 {
  font-size: 12px !important;
}

.font-size-13 {
  font-size: 13px !important;
}

.font-size-14 {
  font-size: 14px !important;
}

.font-size-15 {
  font-size: 15px !important;
}

.font-size-16,
.font-size-little-more {
  font-size: 16px !important;
}

.font-size-18,
.font-size-more {
  font-size: 18px !important;
}

.font-size-20,
.font-size-big-more {
  font-size: 20px !important;
}

.font-size-22 {
  font-size: 22px !important;
}

.font-size-24 {
  font-size: 24px !important;
}

.font-size-26 {
  font-size: 26px !important;
}

.font-size-28 {
  font-size: 28px !important;
}

.font-size-30 {
  font-size: 30px !important;
}

.font-size-40 {
  font-size: 40px !important;
}

/* font color */
.font-default-color,
.font-primary {
  color: #0099db;
}

.font-color-blue,
.font-color-blue-bold {
  color: #22baff;
}

.font-color-green,
.font-color-green-bold {
  color: #08b950;
}

.font-color-red,
.font-color-red-bold {
  color: #f7132a;
}

.font-color-black,
.font-color-black-bold {
  color: #161515;
}

.font-color-gray {
  color: #999;
}

.font-color-whiteing {
  color: #ffffff;
}

.font-bold,
.font-color-blue-bold,
.font-color-green-bold,
.font-color-red-bold,
.font-color-black-bold {
  font-weight: 600;
}

.font-normal,
.label-font-normal {
  font-weight: normal;
}

.left {
  float: left;
}

.right {
  float: right;
}

/* add padding */

.padding-little-more,
.add-padding-lr-6,
.add-padding-l-6 {
  padding-left: 6px !important;
}

.add-padding-l-8 {
  padding-left: 8px;
}

.add-padding-l-10 {
  padding-left: 10px;
}

.add-padding-l-12 {
  padding-left: 12px;
}

.add-padding-l-16 {
  padding-left: 16px;
}

.add-padding-l-20 {
  padding-left: 20px;
}

.add-padding-lr-20 {
  padding-left: 20px;
  padding-right: 20px;
}

.add-padding-l-24 {
  padding-left: 24px;
}

.add-padding-l-28 {
  padding-left: 28px;
}

.add-padding-l-30 {
  padding-left: 30px;
}

.add-padding-l-40 {
  padding-left: 40px;
}

.add-padding-l-50 {
  padding-left: 50px;
}

.padding-little-more,
.add-padding-lr-6,
.add-padding-r-6 {
  padding-right: 6px !important;
}

.add-padding-r-8 {
  padding-right: 8px;
}

.add-padding-r-2 {
  padding-right: 2px;
}

.add-padding-r-10 {
  padding-right: 10px;
}

.add-padding-r-12 {
  padding-right: 12px;
}

.add-padding-r-16 {
  padding-right: 16px;
}

.add-padding-r-20 {
  padding-right: 20px;
}

.add-padding-r-24 {
  padding-right: 24px;
}

.add-padding-r-30 {
  padding-right: 30px;
}

.add-padding-l-50 {
  padding-right: 50px;
}

.add-padding-t-4 {
  padding-top: 4px;
}

.add-padding-t,
.add-padding-t-6 {
  padding-top: 6px;
  display: inline-block;
}

.add-padding-t-8 {
  padding-top: 8px;
}

.add-little-padding-tb,
.add-padding-t-10 {
  padding-top: 10px;
}

.add-padding-t-12 {
  padding-top: 12px;
}

.add-padding-t-16 {
  padding-top: 16px;
}

.add-padding-tb,
.add-padding-t-20 {
  padding-top: 20px;
}

.add-padding-t-24 {
  padding-top: 24px;
}

.add-padding-t-30 {
  padding-top: 30px;
}

.add-padding-tb40,
.add-padding-t-40 {
  padding-top: 40px;
}

.add-padding-t-50 {
  padding-top: 50px;
}

.add-padding-tb60,
.add-padding-t-60 {
  padding-top: 60px;
}

.add-padding-b-0 {
  padding-bottom: 0;
}

.add-padding-b-4 {
  padding-bottom: 4px;
}

.add-padding-b6,
.add-padding-b-6 {
  padding-bottom: 6px;
}

.add-padding-b8,
.add-padding-b-8 {
  padding-bottom: 8px;
}

.add-little-padding-tb,
.add-padding-b-10 {
  padding-bottom: 10px;
}

.add-padding-b-12 {
  padding-bottom: 12px;
}

.add-padding-b-16 {
  padding-bottom: 16px;
}

.add-padding-tb,
.add-padding-b-20 {
  padding-bottom: 20px;
}

.add-padding-b-24 {
  padding-bottom: 24px;
}

.add-padding-b-30 {
  padding-bottom: 30px;
}

.add-padding-tb40,
.add-padding-b-40 {
  padding-bottom: 40px;
}

.add-padding-tb60,
.add-padding-b-60 {
  padding-bottom: 60px;
}

.add-padding-tb40,
.add-padding-b-40 {
  padding-bottom: 40px;
}

.add-padding-tb60,
.add-padding-b-60 {
  padding-bottom: 60px;
}

.space-pre-line {
  white-space: pre-line;
}

.lg-px-5 {
  padding-left: 5px;
  padding-right: 5px;
}


.lg-pt-10 {
  padding-top: 10px !important;
}

.lg-pa-2 {
  padding: 2px;
}

.lg-pa-4 {
  padding: 4px;
}

.lg-pa-5 {
  padding: 5px;
}



.lg-pa-10 {
  padding: 10px;
}

.lg-pa-12 {
  padding: 12px;
}

.lg-py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.lg-pl-18 {
  padding-left: 18px;
}

.list-disc {
  list-style: disc !important;
}

.radius-6 {
  border-radius: 6px;
}

.text-default {
  color: #111c1c !important;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

/* remove padding */
.remove-padding-l,
.remove-padding-l-0,
.remove-padding-lr,
.remove-padding-lr-0 {
  padding-left: 0 !important;
}

.remove-padding-r,
.remove-padding-r-0,
.remove-padding-lr,
.remove-padding-lr-0 {
  padding-right: 0 !important;
}

.remove-padding-t-0,
.remove-padding-tb,
.remove-padding-tb-0 {
  padding-top: 0;
}

.remove-padding-b-0,
.remove-padding-tb,
.remove-padding-tb-0 {
  padding-bottom: 0;
}

.remove-padding {
  padding: 0;
}

/*add margin*/

.add-margin-l-4 {
  margin-left: 4px;
}

.add-margin-lr-6,
.add-margin-l-6 {
  margin-left: 6px !important;
}

.add-margin-l-8 {
  margin-left: 8px;
}

.add-margin-l-10 {
  margin-left: 10px;
}

.add-margin-l12,
.add-margin-l-12 {
  margin-left: 12px;
}

.add-margin-l-16 {
  margin-left: 16px;
}

.add-margin-l20,
.add-margin-l-20 {
  margin-left: 20px;
}

.add-margin-l-24 {
  margin-left: 24px;
}

.add-margin-l-30 {
  margin-left: 30px;
}

.add-margin-l-35 {
  margin-left: 35px !important;
}

.add-margin-l-40 {
  margin-left: 40px !important;
}

.add-margin-r-4 {
  margin-right: 4px !important;
}

.add-margin-lr-6,
.add-margin-r-6 {
  margin-right: 6px !important;
}

.add-margin-r-8 {
  margin-right: 8px;
}

.add-margin-r-10 {
  margin-right: 10px;
}

.add-margin-r-12 {
  margin-right: 12px;
}

.add-margin-r-16 {
  margin-right: 16px;
}

.add-margin-r-20 {
  margin-right: 20px;
}

.add-margin-r-24 {
  margin-right: 24px;
}

.add-margin-r-30 {
  margin-right: 30px;
}

.add-margin-r-35 {
  margin-right: 35px;
}

.add-margin-r-180 {
  margin-right: 180px;
}
.add-margin-t-2 {
  margin-top: 2px;
}
.add-margin-t-4 {
    margin-top: 4px;
}

.add-margin-t6,
.add-margin-tb-6,
.add-margin-t,
.add-margin-t-6 {
  margin-top: 6px;
}

.add-margin-t-8 {
  margin-top: 8px;
}

.add-margin-t10,
.add-margin-bt10,
.add-margin-t-10 {
  margin-top: 10px;
}

.add-margin-t-12 {
  margin-top: 12px;
}

.add-margin-t-16 {
  margin-top: 16px;
}

.add-margin-t-18 {
  margin-top: 18px;
}

.add-margin-t-20 {
  margin-top: 20px;
}

.add-margin-t-24 {
  margin-top: 24px;
}

.add-margin-t-30 {
  margin-top: 30px;
}
.add-margin-t-32 {
  margin-top: 32px;
}

.add-margin-t-36 {
  margin-top: 36px;
}

.add-margin-tb40,
.add-margin-t-40 {
  margin-top: 40px;
}

.add-margin-tb60,
.add-margin-t-60 {
  margin-top: 60px;
}

.add-margin-b6,
.add-margin-tb-6,
.add-margin-b-6,
.margin-B6 {
  margin-bottom: 6px;
}

.add-margin-b8,
.add-margin-b-8 {
  margin-bottom: 8px;
}

.add-margin-bt10,
.add-margin-b-10 {
  margin-bottom: 10px;
}

.add-margin-b-12 {
  margin-bottom: 12px;
}

.add-margin-b-16 {
  margin-bottom: 16px;
}

.bottom-margin-20,
.add-margin-b-20 {
  margin-bottom: 20px;
}

.add-margin-b-24 {
  margin-bottom: 24px;
}

.add-margin-b-30 {
  margin-bottom: 30px;
}

.add-margin-b-36 {
  margin-bottom: 36px;
}

.add-margin-tb40,
.add-margin-b-40 {
  margin-bottom: 40px;
}

.add-margin-tb60,
.add-margin-b-60 {
  margin-bottom: 60px;
}

/*remove margin*/
.remove-margin-l,
.remove-margin-l-0,
.remove-margin-lr,
.remove-margin-lr-0 {
  margin-left: 0 !important;
}

.remove-margin-r,
.remove-margin-r-0,
.remove-margin-lr,
.remove-margin-lr-0 {
  margin-right: 0 !important;
}

.remove-margin-t-0,
.remove-margin-tb,
.remove-margin-tb-0 {
  margin-top: 0;
}

.remove-margin-b-0,
.remove-margin-tb,
.remove-margin-tb-0 {
  margin-bottom: 0;
}

.remove-margin {
  margin: 0;
}

/*reduce margin*/

.reduce-margin-t-16 {
  margin-top: -16px;
}

/*border*/
.remove-border,
.border-none {
  border: none !important;
}

.add-border-dashed-b,
.add-border-dashed-gray-b,
.add-border-dashed-gray {
  border-bottom: 1px dashed #ddd;
}

.add-border-dashed-gray-l,
.add-border-dashed-gray {
  border-left: 1px dashed #ddd;
}

.add-border-dashed-gray-r,
.add-border-dashed-gray {
  border-right: 1px dashed #ddd;
}

.add-border-dashed-gray-t,
.add-border-dashed-gray {
  border-top: 1px dashed #ddd;
}

.add-border-solid-red,
.label-border,
.select-btn {
  border: 1px solid #e10d0d;
}

.add-border-dashed-gray-2 {
  border: 2px dashed #ddd;
}

/*lg 分割线*/
.lg-divider {
  border-top: 1px solid #ccc;
  margin: 8px -15px;
}

.lg-bottom-divider {
  border-bottom: 1px solid #edf1f2;
}

.lg-b-m15-divider {
  border-bottom: 1px solid #edf1f2;
  margin: 15px auto;
}

.lg-b-m8-divider {
  border-bottom: 1px solid #edf1f2;
  margin: 8px auto;
}

.lg-b-full-divider {
  border-bottom: 1px solid #edf1f2;
  margin: 8px -15px;
}

.lg-b-dashed-divider {
  border-bottom: 1px dashed #edf1f2;
}

/*overflow text ellipsis*/
.overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-interrupt {
  white-space: normal;
  word-break: break-word
}

.span-text-middle {
  font-size: 18px;
  max-width: 100px;
  display: inline-block;
  vertical-align: middle;
}

/*overflow text break*/
.break-word {
  word-break: break-all;
  word-wrap: break-word;
}

.word-break {
  word-wrap: break-word;
  word-break: break-word;
}

.overflow-ellipsis-two {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.overflow-ellipsis-three {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

/*element display*/

.tag-display,
.display-none {
  display: none;
}

.display-Ib,
.display-ib {
  display: inline-block !important;
}

.display-tc,
.display-TC,
.display-Tc {
  display: table-cell !important;
}

.display-tb,
.display-Tb {
  display: table !important;
}

.display-b {
  display: block;
}

/*change more btn width*/
.btn-change-width {
  padding-left: 26px;
  padding-right: 26px;
}

.btn-change-more-width {
  padding-right: 36px;
  padding-left: 36px;
}

/*default right view css */
.right-view-style {
  background-color: #fff;
  min-height: 540px;
  margin-left: 0;
  margin-right: 0;
}

/*content vertical middle*/
.content-vertical-middle,
.table-vertical-middle {
  vertical-align: middle !important;
}

/*opacity*/
.remove-opacity {
  opacity: 1;
}

.add-opacity-6 {
  opacity: 0.6;
}

.opacity-40 {
  opacity: 0.4;
}

.opacity-70 {
  opacity: 0.7;
}

/*firefox*/
.reset-input-file {
  height: auto;
  padding: 5px 12px;
}

/*head image*/
.head-image-wh-60 {
  width: 60px;
  height: 60px;
}

.width-600 {
    width: 600px;
}

/* table tr th*/
thead > tr > th {
  text-align: center;
  vertical-align: middle !important;
}

.expand-table-width > tbody > tr > td,
.expand-table-width > thead > tr > th {
  min-width: 150px;
}

.el-dialog-custom .el-dialog__body {
  padding: 10px 20px !important;
}

/*hide table header*/
.lg-table-hide-header > thead {
  display: none !important;
}

.table.table-sm > thead > tr > th {
  padding: 4px 15px;
}

.table.table-sm > tbody > tr > td {
  padding: 6px 15px;
}

/* dot */
.black-dot {
  border: 2px solid #000;
  display: inline-block;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 10px;
}

/*background color */
.bg-pale-blue {
  background-color: #f0f8fa;
}

/*checkbox size*/
.checkbox-size-16 {
  width: 16px;
  height: 16px;
}

.cover-line {
  position: absolute;
  width: 100%;
  height: 4px;
  background-color: #fff;
  left: 0;
  bottom: -2px;
}

/*table >td ��ֱ����*/
.td-middle .table thead tr td,
.td-middle .table tbody tr td,
.td-middle .table thead tr th {
  vertical-align: middle;
}

.thead-border-1 > th,
.thead-border-1 > td {
  border-bottom-width: 1px !important;
}

.min150-column-width > thead > tr > th {
  min-width: 150px;
}

/*��Ԫ���С����?*/
.cell-size-limit > th,
.cell-size-limit > td {
  max-width: 120px;
  min-width: 60px;
}

/*close model*/
.close-model {
  text-decoration: none;
  font-size: 24px;
  font-weight: normal;
}

.lg-underline {
  text-decoration-line: underline;
}

/*lg checkbox*/
.lg-checkbox {
  position: relative;
  display: block;
  margin-top: 4px;
  margin-bottom: 4px;
}

.min-height-0 {
  min-height: 0;
}

.lg-checkbox label {
  min-height: 20px;
  margin-bottom: 0;
  font-weight: 400;
  cursor: pointer;
}

/* handle son element if parent is checkbox or forbid checked */
.forbid-checked > .lg-checked {
  display: none;
}

.lg-checkbox > .lg-unchecked {
  display: none;
}

/*sunshuai   start*/
.oddtr {
}

.eventr {
  background-color: #f4f7fe;
}

.error-if {
  border: 1px solid #a94442;
  border-radius: 5px;
}

.error-if > button {
  border-bottom: none;
  border-top: none;
}

.btn-error {
  border-color: #f05050 !important;
}

.checkbox_blue {
  line-height: 24px;
  position: relative;
}

.checkbox_blue label input[type="checkbox"] {
  display: none;
}

.checkbox_blue label input[type="checkbox"]:checked + span:before,
.checkbox_blue label input[type="checkbox"]:not(:checked) + span:before {
  background: #fff
    url(https://d2urtjxi3o4r5s.cloudfront.net/images/web_checkbox.png);
  content: " ";
  height: 22px;
  left: 0;
  position: absolute;
  width: 22px;
}

.checkbox_blue label input[type="checkbox"]:not(:checked):focus + span:before,
.checkbox_blue label input[type="checkbox"]:not(:checked) + span:hover:before {
  background-position: -24px 0;
}

.checkbox_blue label input[type="checkbox"]:checked + span:before {
  background-position: -48px 0;
}

.checkbox_blue
  label
  input[type="checkbox"][disabled]:not(:checked)
  + span:before {
  background-position: -72px 0;
}

.checkbox_blue label input[type="checkbox"][disabled]:checked + span:before {
  background-position: -96px 0;
}

.checkbox_blue label {
  margin-left: 30px;
}

.switch_blue {
  min-height: 24px;
  position: relative;
  box-sizing: border-box;
  left: 0;
  margin: 0 auto;
  padding: 0;
  top: 0;
  width: 60px;
  height: 24px;
}

/* text-indent: -9999px;*/
.switch_blue label {
  background-color: #e0e0e0;
  border-radius: 24px;
  cursor: pointer;
  display: inline-block;
  height: 24px;
  position: relative;

  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 48px;
}

.switch_blue label:after {
  background-color: #fff;
  border-radius: 20px;
  content: "";
  height: 20px;
  left: 2px;
  position: absolute;
  top: 2px;
  -webkit-transition: -webkit-transform 0.3s;
  -o-transition: -o-transform 0.3s;
  transition: transform 0.3s;
  width: 20px;
}

.switch_blue label:before {
  content: "OFF";
  left: 24px;
  position: absolute;
  top: 4px;
  font-size: 12px;
  color: #fff;
  -webkit-transition: -webkit-transform 0.3s;
  -o-transition: -o-transform 0.3s;
  transition: transform 0.3s;
}

.switch_blue.open label {
  background-color: #8c8;
}

.switch_blue.open label:after {
  -webkit-transform: translateX(24px);
  -ms-transform: translateX(24px);
  -o-transform: translateX(24px);
  transform: translateX(24px);
}

.switch_blue.open label:before {
  content: "ON";
  left: 7px;
  position: absolute;
  font-size: 12px;
  top: 4px;
  color: #fff;
  -webkit-transition: -webkit-transform 0.3s;
  -o-transition: -o-transform 0.3s;
  transition: transform 0.3s;
}

.switch_blue.open .on {
  display: inline;
}

.switch_blue.open .off {
  display: none;
}

.scroll {
  max-height: 350px;
  min-width: 250px;
  overflow-x: hidden;
  overflow-y: auto;
  scroll-snap-points-y: repeat(50px);
  scroll-snap-type: mandatory;
}

.border_1 > table {
  border: solid #40bcc4 2px;
}

.border_1 > table > thead > tr > td {
  border: solid #399ea6 1px;
}

.border_1 > table > tbody > tr > td {
  border: solid #ddd 1px;
}

.border_2 > table {
  border: solid #67abaa 2px;
}

.border_2 > table > thead > tr > td {
  border: solid #399ea6 1px;
}

/*.border_2 > table > tbody > tr > td{*/
/*border: solid #ddd 1px;*/
/*}*/

.subList > td {
  border: solid #0095c1 1px;
}

.measure > td {
  border: solid #d4d4d4 1px;
}

.scroll > div {
  min-width: 250px;
  height: 30px;
  word-break: break-all;
  word-wrap: break-word;
  line-height: 30px;
}

.dropdown-menu1 {
  top: auto;
}

/*sunshuai   end*/
/*reset col padding*/
.lg-row-col-padding-0.row > .col-sm-12 {
  padding-left: 0;
  padding-right: 0;
}

.btn-blue {
  color: #fff;
  background-color: #01d5d5;
  border-color: #02dbb3;
}

.btn-blue:focus,
.btn-blue.focus {
  color: #fff;
  background-color: #0ed8c5;
  border-color: #0cbdac;
}

.btn-blue:hover {
  color: #fff;
  background-color: #0ed8c5;
  border-color: #0cbdac;
}

.btn-blue-outline {
  border-color: #10b3b7 !important;
  padding: 7px 15px;
}

.restPopover + .popover {
  min-width: 300px !important;
}

.ui-grid-cell-contents {
  padding: 5px;
  line-height: 26px;
}

.remove-dropdown-btn-shadow .btn-group.open .dropdown-toggle {
  box-shadow: none;
}

button[disabled="disabled"],
button[disabled="true"],
.disabled {
  cursor: not-allowed !important;
  opacity: 0.6;
}

.grid {
  width: 100%;
  /*height: 520px;*/
}

.ui-grid-icon-angle-down {
  display: none;
}

.dropdown-menu > div > a {
  padding: 5px 15px;
  display: block;
}

.dropdown-menu > div > a:hover,
.dropdown-menu > div > a:focus,
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus,
.dropdown-menu > div > .active {
  background-image: none;
  filter: none;
  background-color: #edf1f2 !important;
  color: #111c1c;
}

.lg-sort-wrapper {
  display: inline-block;
  height: 12px;
}

.lg-sort-asc {
  display: block;
  top: -1px;
  left: 10px;
}

.lg-sort-desc {
  display: block;
  top: 5px;
  left: 10px;
}

.asc > .lg-sort-asc {
  top: 2px;
}

.asc > .lg-sort-desc {
  display: none;
}

.desc > .lg-sort-asc {
  display: none;
}

.desc > .lg-sort-desc {
  top: 3px;
}

.sort-header-pointer > th {
  cursor: pointer;
}

.pagination > .active > a,
.pagination > .active > a:focus,
.pagination > .active > a:hover,
.pagination > .active > span,
.pagination > .active > span:focus,
.pagination > .active > span:hover {
  background-color: #10b3b7;
  border-color: #10b3b7;
}

.sortable-ghost {
  background-color: #eee;
}

.virtualLine {
  border-bottom: solid 1px #eee;
  width: 105%;
}

.reportGroupColor {
  background-color: #b9dfe0;
}

.lg-pointer {
  cursor: pointer;
}

.cursor-disabled {
  cursor: not-allowed !important;
}

.lg-reset-select-icon {
  position: absolute;
  right: 2px;
  top: 2px;
  padding: 8px 8px 8px 20px;
  z-index: 2;
  background: #fff;
  text-align: center;
  display: inline-block;
}

.period-status {
  display: inline-block;
  width: 25px;
  height: 25px;
  position: absolute;
  bottom: -4px;
  right: -4px;
}

.unposi-period-status {
  display: inline-block;
  width: 25px;
  height: 25px;
}

/*.period-status.lock,.unposi-period-status.lock{
     background: url(../../portfolio/img/lock.png) left top no-repeat;
}
.period-status.unlock,.unposi-period-status.unlock{
    background: url(../../portfolio/img/unlock.png) left top no-repeat;
} */
.mx-w-md {
  max-width: 120px;
}

.mx-w-md-max-200 {
  max-width: 200px;
}

.dropdown-menu-auto {
  width: 1200px;
  right: auto;
  top: 30px;
  left: -600px;
}

@media only screen and (min-width: 1401px) {
  .dropdown-menu-auto {
    left: -450px;
    width: 850px;
  }
}

@media only screen and (max-width: 1400px) {
  .dropdown-menu-auto {
    left: -450px;
    width: 850px;
  }
}

@media only screen and (max-width: 1300px) {
  .dropdown-menu-auto {
    left: -350px;
    width: 850px;
  }
}

@media only screen and (max-width: 1200px) {
  .dropdown-menu-auto {
    left: -450px;
    width: 850px;
  }
}

@media only screen and (max-width: 1100px) {
  .dropdown-menu-auto {
    left: -400px;
    width: 850px;
  }
}

@media screen and (max-width: 1000px) {
  .dropdown-menu-auto {
    right: auto;
    left: -400px;
    width: 700px;
  }
}

@media screen and (max-width: 900px) {
  .dropdown-menu-auto {
    left: -400px;
    width: 500px;
  }
}

@media screen and (max-width: 800px) {
  .dropdown-menu-auto {
    left: -350px;
    width: 400px;
  }
}

@media screen and (max-width: 700px) {
  .dropdown-menu-auto {
    right: auto;
    left: -250px;
    width: 300px;
  }
}

@media screen and (max-width: 767px) {
  .el-dialog {
    max-width: 100% !important;
  }
}


@media screen and (max-width: 650px) {
  .dropdown-menu-auto {
    left: 0;
    width: 300px;
  }
}

.pie-menu {
  right: 15px;
  left: 51%;
  top: 36px;
  bottom: 22px;
  overflow-y: auto;
}

.reset-panel .panel {
  display: inline-block;
}

.reset-panel .panel-heading {
  padding: 5px 10px;
}

.reset-panel .panel-body {
  padding: 6px 10px;
}

.rangeChartWrapper {
  position: relative;
  width: 100%;
  height: 26px;
  cursor: pointer;
}

.dashedLine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  border: 1px dashed #ccc;
  background: url("https://d2urtjxi3o4r5s.cloudfront.net/images/bias.png")
    repeat-x left center #fafbfc;
  height: 100%;
}

.rangeChart {
  position: absolute;
  border: 1px solid #ccc;
  height: 100%;
  background-color: #fff;
}

.actualChart {
  position: absolute;
  border: 1px solid #61a115;
  height: 0;
  top: 50%;
  margin-top: -1px;
}

.actualChart:before {
  border: 1px solid #61a115;
  height: 8px;
  width: 0;
  position: absolute;
  content: "";
  left: -2px;
  top: -4px;
}

.actualChart:after {
  border: 1px solid #61a115;
  height: 8px;
  width: 0;
  position: absolute;
  content: "";
  right: -2px;
  top: -4px;
}

.expectChart {
  position: absolute;
  background-color: #ecfac7;
  height: 100%;
}

.radius-note {
  position: absolute;
  left: 50%;
  margin-left: -4px;
  top: -4px;
  display: inline-block;
  border: 4px solid #61a115;
  border-radius: 50%;
}

.bg-tpt {
  background-color: transparent !important;
}

.bg-alice-blue {
  background-color: #f0f3f4;
}

.label.label-sm {
  color: #fff;
  padding: 1px 10px;
  font-size: 14px;
}

.has-error .form-control,
.has-error .assert-color,
.has-error .i-checks i {
  border-color: #f5201c !important;
  box-shadow: none !important;
}

.tab-btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
}

.tab-btn:active,
.tab-btn.active {
  background-image: none;
  outline: 0;
}

.tab-btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.tab-btn-default:active,
.tab-btn-default.active {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.clear-btn-border .tab-btn {
  border: none;
  box-shadow: none;
}

.clear-btn-border .isClick.tab-btn {
  padding: 4px 11px 5px 11px;
  position: relative;
  z-index: 4;
  border-left: 1px solid #ddd;
  background-color: #f6f9fb;
  border-top: 2px solid #06c7cc;
  border-right: 1px solid #ddd;
}

.objective-btn {
  display: inline-block;
  padding: 6px 20px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid #ccc;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  color: #333;
  background-color: #eff3ff;
  margin-right: 4px;
}

.objective-btn.is-current {
  background-color: #fff;
}

.btn-primary.active.focus,
.btn-primary.active:focus,
.btn-primary.active:hover,
.btn-primary:active.focus,
.btn-primary:active:focus,
.btn-primary:active:hover,
.open > .dropdown-toggle.btn-primary.focus,
.open > .dropdown-toggle.btn-primary:focus,
.open > .dropdown-toggle.btn-primary:hover {
  background-color: #0e9ca0;
  border-color: #0d8e91;
}

.btn-group .btn.active,
.btn-group .btn:active {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.bg-tr-danger {
  background-color: #feeaeb !important;
}

.bg-tr-danger > td {
  background-color: #feeaeb !important;
}

.bg-tr-primary {
  background-color: #eaf7fd !important;
}

.bg-tr-primary > td {
  background-color: #eaf7fd !important;
}

.report-title {
  font-size: 20px;
  font-weight: 700;
}

.report-title a {
  font-size: 18px;
}

#needs_1 .c3-texts text,
#strengths_1 .c3-texts text,
#needs_3 .c3-texts text,
#strengths_3 .c3-texts text {
  font-family: Arial;
  fill: #111c1c !important;
  font-size: 18px;
}

#needs_2 .c3-texts text,
#needs_4 .c3-texts text,
#strengths_2 .c3-texts text,
#strengths_4 .c3-texts text {
  font-family: Arial;
  fill: #47555d !important;
  font-size: 14px;
}

.c3-axis-x,
.c3-axis-y,
.c3-legend-item {
  fill: #47555d;
}

#normal .c3-texts text,
#terms .c3-texts text {
  font-family: Arial;
  fill: #47555d !important;
  font-size: 12px;
}

.c3-axis-x {
  font-family: Arial;
  fill: #47555d;
  font-size: 12px;
}

.c3-axis-y {
  font-family: Arial;
  fill: #47555d;
  font-size: 12px;
}

.c3-legend-item {
  font-family: Arial;
  fill: #47555d;
  font-size: 12px;
}

.c3-legend-background {
  opacity: 0.75;
  fill: #f0f3f4 !important;
  stroke: #f0f3f4 !important;
  stroke-width: 1;
}

.lessonPlan {
  border-radius: 5px;
  border: solid 1px #3e718c;
  background-color: white;
}

.lessonPlan .measureAbbr {
  background-color: #0174b5;
  max-width: 25%;
  color: white;
  padding: 5px 8px;
  float: left;
}

.lessonPlan .measureName {
  color: #0174b5;
  min-width: 100px;
  max-width: 70%;
  min-height: 35px;
  padding-top: 5px;
  line-height: 1;
  margin-left: 10px;
  float: left;
  vertical-align: middle;
}

.lessonPlan .notes {
  border: solid 1px #ddd;
  font-weight: 800;
  padding: 1px 5px;
  text-align: center;
  color: #3ab2a6;
  font-size: 16px;
  display: inline-block;
}

.lessonPlan .notes:hover {
  background-color: #eee;
}

.lessonDivider {
  border-bottom: solid 1px #ddd;
  padding-bottom: 10px;
  padding-top: 4px;
}

.modalLine {
  width: 90%;
  margin-left: 30px;
  margin-right: 10px;
  border-bottom: solid 1px #eee;
  float: left;
}

.materials {
  font-size: 30px;
  margin-right: 30px;
  display: inline-block;
  width: 55px;
  height: 55px;
  border-radius: 50%;
}

.materials > i {
  color: white;
  margin-left: 22%;
  margin-top: 22%;
}

.mainDropmenu > li {
  padding-top: 4px;
  padding-bottom: 5px;
}

.mainDropmenu > li:hover {
  padding-top: 4px;
  padding-bottom: 5px;
  background-color: #edf1f2 !important;
}

.hide {
  display: none !important;
}

.panel-blue,
.no-border {
  border: none;
}

.panel-blue > .panel-heading {
  color: #fff;
  background-color: #28bab9;
  border-color: #28bab9;
}

.panel-blue > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #28bab9;
}

.panel-blue > .panel-heading .badge {
  color: #28bab9;
  background-color: #fff;
}

.panel-blue > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #28bab9;
}

.bg-green {
  background-color: #ddf6d5;
}

.bg-white {
  background-color: #ffffff;
}

.copy-header {
  position: fixed;
}

.border-light {
  border-left-color: #bbb !important;
  border-right-color: #bbb !important;
}

.border-t-light {
  border-top-color: #bbb !important;
}

.border-b-light {
  border-bottom-color: #bbb !important;
}

.border-t-lighter {
  border-top: 2px solid #bbb !important;
}

.border-r-lighter {
  border-right: 2px solid #bbb !important;
}

.border-b-lighter {
  border-bottom: 2px solid #bbb !important;
}

.border-l-lighter {
  border-left: 2px solid #bbb !important;
}

.dashboard-active {
  background-color: #edf1f2 !important;
}

.title_nav {
  font-size: 16px;
  border-bottom: solid 1px #ddd;
  line-height: 40px;
}

.tab_active {
  padding-bottom: 9px;
  border-bottom: solid 2px #10b3b7;
}

.observation_content {
  display: inline-block;
  background-color: white;
  width: 18%;
  height: 180px;
}

.at-glance {
  padding: 0;
}

.glance_content {
  display: flex;
  justify-content: center;
  height: 80px;
  margin-right: 2px;
}

.us-glance {
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 0;
}

.us-glance-content {
  margin-right: 10px;
  position: relative;
  display: inline-block;
  background-color: #fff;
  width: 16%;
  height: 80px;
}

.bottom-line {
  height: 2px;
  width: 100%;
}

.parent_content {
  display: inline-block;
  background-color: white;
  width: 33.1%;
  margin-left: 0.1%;
  margin-right: 0.1%;
  height: 130px;
}

.glance_left {
  height: 100%;
  margin-right: 10px;
}

.right-line {
  float: right;
  height: 30px;
  margin-top: 25px;
  width: 2px;
}

.glance_right {
  display: flex;
  margin-left: 10px;
  align-items: center;
  flex-wrap: wrap;
  text-align: center;
  align-content: center;
}

.glance_right div {
  width: 100%;
}

.us-glance-left,
.us-glance-right {
  position: absolute;
  display: inline-block;
}

.us-glance-left {
  top: 24px;
}

.us-glance-right {
  top: 10px;
  left: 40%;
}

.glance_left i {
  display: inline-block;
  vertical-align: middle;
  height: 100%;
}

.glance_icon {
  color: white;
  width: 35px;
  height: 35px;
  margin-left: 10px;
  margin-right: 5px;
  padding-left: 6px;
  padding-top: 5px;
  border-radius: 50%;
}

.singleSelect {
  background-color: #e8f9e9 !important;
}

.child_icon {
  width: 50%;
  height: 50%;
  padding: 1% 2.5%;
  float: left;
}

.child_icon img,
.child_icon div {
  height: 103%;
  width: 100%;
}

.simulatedBox {
  padding: 10px 15px;
  min-width: 100px;
  min-height: 60px;
  border: solid 1px #ddd;
  border-radius: 2%;
}

.group-list-pic {
  display: inline-block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  background-color: steelblue;
  vertical-align: middle;
}

.group-list-name {
  display: inline-block;
  vertical-align: middle;
}

.selectedItem {
  float: left;
  background-clip: padding-box;
  background-color: #eeeeee;
  border: 1px solid #cccccc;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  color: #333333;
  cursor: default;
  font-size: 14px;
  line-height: 13px;
  margin: 3px 6px 3px 0;
  padding: 3px 5px;
  position: relative;
}

.selected-delete {
  /* background: url(../../core/img/chosen-sprite.png) right top no-repeat; */
  display: inline-block;
  font-size: 1px;
  height: 10px;
  margin-bottom: -1px;
  width: 12px;
  cursor: pointer;
}

.selected-delete:hover {
  background-position: right -11px;
}

.group-list-pic {
  display: inline-block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
  background-color: steelblue;
  vertical-align: middle;
}

.group-list-name {
  display: inline-block;
  vertical-align: middle;
}

.disabledRate {
  cursor: not-allowed;
}

.title-label {
  position: absolute;
  border-bottom-color: transparent;
  border-bottom-style: solid;
  border-bottom-width: 25px;
  border-left-color: transparent;
  border-left-style: solid;
  border-left-width: 25px;
  border-right-color: green;
  border-right-style: solid;
  border-right-width: 25px;
  border-top-color: green;
  border-top-style: solid;
  border-top-width: 25px;
  right: 0;
  top: 0;
}

.title-label-color-info {
  border-top-color: #0d8e91;
  border-right-color: #0d8e91;
}

.title-label-color-success {
  border-top-color: #3c763d;
  border-right-color: #3c763d;
}

.title-label-color-primary {
  border-top-color: #6051b5;
  border-right-color: #6051b5;
}

.selectedTopic {
  border-bottom-color: #97c23d;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  padding: 0px 15px 11px;
  margin-top: 7px;
  display: inline-block;
}

.date-btn-error {
  border-top: 1px solid #f5201c !important;
  border-right: 1px solid #f5201c !important;
  border-bottom: 1px solid #f5201c !important;
}

/*dashboard样式重置开始*/
.observed_measures {
  width: 30%;
}

.observed_measures1 {
  width: 45%;
}

.observed_notes {
  width: 50%;
  height: 185px;
  border-right: 1px solid #ccc;
}

.dashboardTable td {
  text-align: center;
}

.dashboardTable .progressTable {
  text-align: left;
}

.assessmentTable .panel-body {
  padding: 0;
}

.assessmentTableFramework .panel-body {
  padding-top: 0;
  padding-bottom: 0;
}

.activeBottom {
  border-bottom: 2px solid #8fb83a;
}

.videoBookActive {
  background: #f6f9f9;
}

#dashboardTD td {
  vertical-align: middle;
  padding: 5px 15px;
}

/*dashboard样式重置结束*/
/*approval Queue start*/
.approvalQueue .col-sm-3 {
  padding: 3px;
  margin: 0;
}

.approvalQueue .col-sm-9 {
  padding: 3px;
  margin: 0;
}

.approvalQueue .col-sm-6 {
  padding: 3px;
}

/*approval Queue end*/
/*domainFilter 选择domain的时候显示样式start*/
#domainFilter .fa {
  font-size: 28px;
  margin-right: 15px;
  cursor: pointer;
}

#domainFilter .fa-plus-square-o:before {
  content: "\f0da";
}

#domainFilter .fa-minus-square-o:before {
  content: "\f0d7";
}

#domainFilter input[type="checkbox"] {
  height: 18px;
  width: 18px;
  margin-right: 10px;
}

#domainFilter .ivh-treeview-node-label {
  position: relative;
  top: -4px;
}

.noAliasChildren .popover-content {
  padding: 0;
}

/*domainFilter 选择domain的时候显示样式end*/
.generate-success {
  color: #188e08;
}

/*group edit create frotfolio 的样式*/
.portfolioTabs .tab-content {
  /*overflow: hidden;*/
  /*border: 1px solid #ccc;*/
  border-top: 0;
}

.portfolioTabs select {
  margin: 18px 15px;
  width: 93%;
}

.portfolioTabs .nav-tabs > li.active > a {
  margin-top: 2px;
  border-top: 2px solid #10b3b7 !important;
}

#staffTable th,
#staffTable td {
  padding: 8px 5px;
}

#staffTable .openOrClose th {
  border-top: 0;
  padding-top: 0;
}

#staffTable .openOrClose {
  border-bottom: 0;
  padding-bottom: 0;
}

.cloadBox {
  margin: 0 auto;
  width: 200px;
  height: 240px;
  /* background: url('../../core/img/cload.png') no-repeat bottom center; */
  position: absolute;
  left: 40%;
  top: 20%;
}

.bottomPng {
  width: 100%;
  height: 11px;
  position: absolute;
  /* background: url("../../core/img/yingzi.png") no-repeat top center; */
  bottom: 20px;
  animation: bottomPngRun 1.5s infinite;
}

.topPng {
  width: 100%;
  height: 225px;
  position: absolute;
  /* background: url("../../core/img/genie.png") no-repeat top center; */
  top: 20px;
  animation: topPngRun 1.5s infinite;
}

.yanPng {
  width: 13px;
  height: 19px;
  position: absolute;
  /* background: url("../../core/img/yan.png") repeat 0 0; */
  top: 45px;
  left: 45%;
  animation: yanBackgroundRun 2s infinite;
}

@keyframes bottomPngRun {
  0% {
    bottom: 20px;
  }

  10% {
    bottom: 23px;
  }

  20% {
    bottom: 25px;
  }

  30% {
    bottom: 27px;
  }

  40% {
    bottom: 30px;
  }

  50% {
    bottom: 33px;
  }

  60% {
    bottom: 30px;
  }

  70% {
    bottom: 28px;
  }

  80% {
    bottom: 25px;
  }

  90% {
    bottom: 23px;
  }

  100% {
    bottom: 20px;
  }
}

@keyframes topPngRun {
  0% {
    top: 20px;
  }

  10% {
    top: 25px;
  }

  20% {
    top: 30px;
  }

  30% {
    top: 35px;
  }

  40% {
    top: 40px;
  }

  50% {
    top: 43px;
  }

  60% {
    top: 40px;
  }

  70% {
    top: 35px;
  }

  80% {
    top: 30px;
  }

  90% {
    top: 25px;
  }

  100% {
    top: 20px;
  }
}

@keyframes yanBackgroundRun {
  0% {
    background-position-y: 27px;
    display: block;
  }

  14% {
    background-position-y: 23px;
  }

  26% {
    background-position-y: 19px;
  }

  38% {
    background-position-y: 15px;
  }

  50% {
    background-position-y: 11px;
  }

  63% {
    background-position-y: 8px;
  }

  77% {
    background-position-y: 5px;
  }

  90% {
    background-position-y: 3px;
  }

  100% {
    background-position-y: 0;
    display: none;
  }
}

.clearfix .i-checks input[disabled] + i:before,
fieldset[disabled] .i-checks input + i:before {
  background-color: #23b7e5;
}

.pushListMiddle tr th {
  vertical-align: middle !important;
}

.pushListMiddle tr td {
  vertical-align: middle !important;
}

.pushListScroll {
  max-height: 608px;
  overflow-y: scroll;
}

.notesReviewImg {
  height: 88px;
  width: auto;
  margin-right: 3px;
}

.commentBox {
  box-sizing: content-box;
  position: absolute;
  top: 34px;
  left: -1px;
  width: 100%;
  border: 1px solid #ccc;
  z-index: 3;
}

.commentsView {
  width: 610px;
  border: 1px solid #d2d2d2;
  z-index: 30;
  border-radius: 3px;
  position: absolute;
  top: 30px;
  left: -205px;
}

.commentsArrow {
  background: #fff;
  height: 12px;
  position: absolute;
  z-index: 30;
  top: -11px;
  left: 39%;
}

.positionTop {
  width: 610px;
  border: 1px solid #d2d2d2;
  z-index: 30;
  border-radius: 3px;
  position: absolute;
  bottom: 30px;
  left: -205px;
}

.commentsArrowTop {
  background: #fff;
  height: 12px;
  position: absolute;
  z-index: 30;
  bottom: -11px;
  left: 39%;
  transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -o-transform: rotate(180deg);
}

.modal-super-lgs {
  width: 93%;
}

.modal-edit-teacher {
  width: 450px;
}

.borderNone {
  border-bottom: none !important;
}

.bottom-border-3 {
  border-bottom: 3px solid #22b9ff;
  color: #22b9ff !important;
}

.padding-l-r-5 {
  padding-left: 5px;
  padding-right: 5px;
}

.measureDetail {
  position: relative;
  margin-top: 3px;
  margin-right: 5px;
  width: 20px;
}

#notesList .popover {
  max-width: 630px;
  /* optional max width */
  width: intrinsic;
  /* Safari/WebKit uses a non-standard name */
  width: -moz-max-content;
  /* Firefox/Gecko */
  width: -webkit-max-content;
  /* Chrome */
}

.inputFocus {
  border: 1px solid #cfdadd;
}

.inputFocus:focus {
  border: 1px solid #5fcbec;
}

.scrollToTop {
  position: fixed;
  bottom: 60px;
  right: 30px;
  height: 40px;
  width: 40px;
  background: #64ced0;
  line-height: 40px;
  cursor: pointer;
  opacity: 0.5;
}

.scrollToTop:hover,
.scrollToBottom:hover {
  opacity: 1;
}

.scrollToBottom {
  position: fixed;
  top: 160px;
  right: 30px;
  height: 40px;
  width: 40px;
  background: #64ced0;
  line-height: 40px;
  cursor: pointer;
  opacity: 0.5;
}

.modal-create-report {
  width: 700px;
}

.modal-create-report {
  width: 700px;
}

.progress-bar-light {
  background-color: #ccc;
}

.w-full-49 {
  width: 49.5%;
}

.overflowEllipsis:after {
  content: "...";
}

.fileNameWidth {
  max-width: -webkit-calc(100% - 260px);
  max-width: -moz-calc(100% - 260px);
  max-width: calc(100% - 260px);
}

#portfolioReportNav > li > a {
  padding: 6px 10px;
}

.engagementCount .col-sm-6 {
  margin-bottom: 10px;
}

.engagementTopNav .col-sm-12 {
  margin-bottom: 7px;
}

.wfull-overflow-hidden {
  width: 100%;
  overflow-x: hidden;
}

@media screen and (min-width: 1140px) {
  .headerNav .nav > li > a {
    padding: 15px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1140px) {
  .headerNav .nav > li > a {
    padding-left: 10px;
    padding-right: 10px;
  }
}

.display-flex {
  display: -webkit-flex;
  display: flex;
}

.flex-direction-col {
  flex-direction: column;
}

.justify-content {
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}

.justify-content-between {
  -webkit-justify-content: space-between;
  -moz-justify-content: space-between;
  justify-content: space-between;
}

.align-items {
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}

.flex-none {
  flex: none;
}

.flex-auto {
  flex: auto;
  min-width: 0;
}

.flex-auto-vertical {
  flex: auto;
  min-height: 0;
}

.align-items-end {
  -webkit-align-items: end;
  -moz-align-items: end;
  align-items: end;
}

.flex-justify-start {
  justify-content: start;
  margin-left: 0px;
}

.flex-justify-end {
  justify-content: end;
}

.align-content-flex-end {
  align-content: flex-end;
}

.align-content-space-between {
    align-content: space-between;
}

.line-height-12 {
  line-height: 12px !important;
}


/*.reportActive{*/
/*background: #fff;*/
/*border-left: 4px solid #10B3B7!important;*/
/*}*/
.tabActiveBottom {
  border-bottom: 3px solid #10b3b7;
}

.centerList-item-success {
  background-color: #e8fcfc;
}

.model-width-700 {
  width: 700px;
}

.modal-group-size {
  width: 800px;
}

.modal-chat-size {
  width: 700px;
}

.modal-child-size {
  width: 770px;
}

.modal-password-size {
  width: 420px;
}

.modal-parent-code {
  width: 320px;
}

.primary-bottom-3 {
  border-bottom: 3px solid #10b3b7;
  margin-bottom: -1px;
}

.white-space {
  white-space: nowrap;
}

.defaultLineColor {
  background: -webkit-linear-gradient(#37ecba, #72afd3);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(#37ecba, #72afd3);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(#37ecba, #72afd3);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(#37ecba, #72afd3);
  /* 标准的语法 */
}

.selectLineColor {
  background: -webkit-linear-gradient(#f39f3f, #d0352b);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(#f39f3f, #d0352b);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(#f39f3f, #d0352b);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(#f39f3f, #d0352b);
  /* 标准的语法 */
}

.levelBar {
  height: 250px;
  width: 320px;
  position: relative;
  border-bottom: 2px solid #4cb3b8;
  margin-bottom: 60px;
}

.levelBar .ageBar {
  height: 16px;
  width: 100%;
  border-radius: 8px;
  position: absolute;
  bottom: -55px;
  font-size: 0.8rem;
  background: -webkit-linear-gradient(left, #bee721, #0fad9a);
  /* Safari 5.1 - 6.0 */
  background: -o-linear-gradient(left, #bee721, #0fad9a);
  /* Opera 11.1 - 12.0 */
  background: -moz-linear-gradient(left, #bee721, #0fad9a);
  /* Firefox 3.6 - 15 */
  background: linear-gradient(to right, #bee721, #0fad9a);
  /* 标准的语法 */
}

.levelBar .ageBar span {
  position: absolute;
  top: -16px;
}

.levelBar .barBox {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  z-index: 99;
}

.levelBar .bar {
  width: 1.2rem;
  height: 130px;
  margin-bottom: 5px;
  position: relative;
  border-radius: 10px;
}

.levelBar .level {
  width: 100%;
  position: absolute;
  text-align: center;
  font-size: 12px;
  top: -20px;
  color: #4cb3b8;
}

.levelBar .period {
  float: left;
  height: 100%;
  position: relative;
}

.levelBar .periodName {
  position: absolute;
  bottom: -1.5rem;
  font-size: 0.94rem;
  width: 100%;
  text-align: center;
}

.levelBar .line {
  position: absolute;
  bottom: -12px;
  background: #4cb3b8;
  z-index: 999;
  width: 2px;
  height: 8px;
}

.line-height-14 {
  line-height: 14px !important;
}

.line-height-16 {
  line-height: 16px !important;
}

.line-height-18 {
  line-height: 18px !important;
}

.line-height-20 {
  line-height: 20px !important;
}

.line-height-22 {
  line-height: 22px !important;
}

.line-height-24 {
  line-height: 24px !important;
}

.line-height-26 {
  line-height: 26px !important;
}

.line-height-28 {
  line-height: 28px !important;
}

.line-height-30 {
  line-height: 30px !important;
}

.line-height-32 {
  line-height: 32px !important;
}

.line-height-34 {
  line-height: 34px !important;
}

.line-height-36 {
  line-height: 36px !important;
}

.line-height-38 {
  line-height: 38px !important;
}

.line-height-40 {
  line-height: 40px !important;
}

.line-height-42 {
  line-height: 42px !important;
}

.line-height-44 {
  line-height: 44px !important;
}

.line-height-46 {
  line-height: 46px !important;
}

.line-height-48 {
  line-height: 48px !important;
}

.line-height-50 {
  line-height: 50px !important;
}

.home-container {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 768px) {
  .home-container {
    width: 750px;
  }
}

@media (min-width: 992px) {
  .home-container {
    width: 970px;
  }
}

@media (min-width: 1200px) {
  .home-container {
    width: 1170px;
  }
}

@media (min-width: 1300px) {
  .home-container {
    width: 1250px;
  }
}

@media (min-width: 1500px) {
  .home-container {
    width: 1400px;
  }
}

.list-group-item:first-child {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

.list-group {
  border-color: #dee5e7;
}

.homeCenterList {
  height: -webkit-calc(100% + 55px);
  height: -moz-calc(100% + 55px);
  height: calc(100% + 55px);
}

.text-area-100 {
  width: 100%;
  max-width: 100%;
  min-width: 100%;
  min-height: 35px;
}

.header-tabActiveBottom {
  position: absolute;
  bottom: 0;
  left: 0;
  border-bottom: 3px solid #10b3b7;
  width: 100%;
}

#homeCenterList .mCSB_inside > .mCSB_container {
  margin-right: 0px;
}

#homeCenterList .mCSB_container_wrapper > .mCSB_container {
  padding-right: 0;
}

#homeCenterList .mCSB_container_wrapper {
  margin-right: 0;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_draggerRail {
  background-color: #c1c1c1;
  background-color: rgba(0, 0, 0, 0.15);
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background-color: #c1c1c1;
  background-color: #c1c1c1;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
  background-color: #c1c1c1;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCS-learning-genie.mCSB_scrollTools
  .mCSB_dragger.mCSB_dragger_onDrag
  .mCSB_dragger_bar {
  background-color: #c1c1c1;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_buttonUp {
  background-position: -80px 0;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_buttonDown {
  background-position: -80px -20px;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_buttonLeft {
  background-position: -80px -40px;
}

.mCS-learning-genie.mCSB_scrollTools .mCSB_buttonRight {
  background-position: -80px -56px;
}

.label-primary-alt {
  border: 1px solid #10b3b7;
  background: #fff;
  color: #10b3b7;
}

.label-default-alt {
  border: 1px solid #999;
  background: #fff;
  color: #999;
}

.btn-gray-alt {
  color: #d4d4d4;
  background-color: #fff;
  border: 2px dashed #d4d4d4;
}

/*.btn-gray-alt:hover{*/
/*color: #0e9ca0;*/
/*background-color: #fff;*/
/*border: 2px dashed #0e9ca0;*/
/*}*/
.group-edit {
  position: absolute;
  top: 2px;
  right: 3px;
  height: 25px;
  width: 30px;
  border-radius: 2px;
  text-align: center;
}

.group-edit:hover {
  background-color: #e4eaec;
}

.groupPopover + .popover {
  white-space: nowrap;
}

#learninggenie *[role="grid"] .btn {
  border: 0;
  box-shadow: 0 0 !important;
}

#learninggenie .ng-valid-date-disabled {
  padding: 5px;
}

#learninggenie *[role="grid"] .btn-default.active {
  background-color: #19a9d5;
  border-color: #189ec8;
}

#learninggenie *[role="grid"] .btn-default.active span {
  color: white !important;
}

#dashboard-table .progress {
  background: #d6d6d6;
  margin-bottom: 15px;
}

#dashboard-table .progress-xxs {
  height: 5px;
}

.border-1-e {
  border: 1px solid #eee;
}

.border-1-grey {
  border: 1px solid #ddd;
}

.tour-btnNext {
  border: 2px solid #999;
  color: #999;
  background: transparent;
  border-radius: 8px !important;
}

.tour-btnNext:hover {
  border: 2px solid #fff;
  color: #fff;
  background: transparent;
  border-radius: 8px !important;
}

.tour-step-backdrop-parent {
  position: absolute;
  z-index: 1100;
}

.portfolio-l {
  float: left;
  width: 85px;
  font-size: 16px;
  min-height: 30px;
}

.portfolio-r {
  margin-left: 85px;
}

.edit-portfolio .portfolio-r li {
  padding-left: 0;
}

.completedHeight {
  height: 51px;
}

#reportContainer h4 {
  font-size: 16px;
}

.no-list-style {
  list-style: none;
}

.show-error {
  margin-bottom: 0px;
}

.period-active {
  border: 1px solid #10b3b7;
  position: relative;
}

.period-default {
  border: 1px solid #ddd;
}

.message-head {
  font-size: 18px;
  color: #111c1c;
  text-align: center;
  height: 50px;
  line-height: 50px;
  background-color: #e4eaec;
}

.SettingType-view {
  width: 255px;
  background-color: #fff;
}

.options {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #e5e5e5;
  padding-top: 16px;
}

.options.active img {
  color: #fff;
}

.options.active p {
  color: #fff;
}

.options.active {
  background-color: #10b3b7;
}

.headPortrait {
  width: 30px;
  height: 30px;
  border-radius: 50px;
}

.send-message-tab.active {
  color: #10b3b7;
  border-bottom: 3px solid #10b3b7;
}

.pos-abt-t0r0 {
  position: absolute;
  top: -1px;
  right: 0;
}

.sendRecordUL li {
  list-style-type: none;
  margin-top: 3px;
  margin-left: -18px;
}

.sendRecordUL li:hover {
  background-color: #e4eaec;
}

.sendRecordsUl li {
  list-style-type: none;
  line-height: 30px;
}

.show-error {
  margin-bottom: 0px;
}

.tooltip.recordTooltip .tooltip-inner {
  color: black;
  background-color: #fafbfc;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
}

.tooltip.recordTooltip .tooltip-arrow {
  display: none;
}

.period-active {
  border: 1px solid #10b3b7;
  position: relative;
}

.period-default {
  border: 1px solid #ddd;
}

.color-default {
  color: #111c1c !important;
}

#reportContent .active {
  background: #10b3b7;
  color: #fff !important;
}

.scrollbar-new::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 8px;
  /* 高宽分别对应横竖滚动条的尺寸 */
  height: 8px;
}

.scrollbar-new::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: RGBA(182, 182, 182, 0.45);
}

.scrollbar-new::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}

.scrollbar::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 8px;
  /* 高宽分别对应横竖滚动条的尺寸 */
  height: 1px;
}

.scrollbar::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #535353;
}

.scrollbar2 {
  overflow: auto;
  /* 兼容火狐 */
  scrollbar-width: thin;
}

.scrollbar::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}

.scrollbar2::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 6px;
  /* 高宽分别对应横竖滚动条的尺寸 */
  height: 1px;
}

.scrollbar2::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1); */
  background: #dddee0;
}

.scrollbar2::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  /* border-radius: 10px; */
  /* background: #EDEDED; */
}

.scrollbar-y::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 1px;
  /* 高宽分别对应横竖滚动条的尺寸 */
  height: 6px;
}

.scrollbar-y::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  /* background: #dddee0;
  }
  .scrollbar-y::-webkit-scrollbar-track {
    /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

/* 页面左侧联系人列表 样式 */
.chatLeft,
.chatMain {
  float: left;
  min-height: 130px;
  height: 100%;
}

.chatLeft {
  margin-left: -100%;
  width: 300px;
  background: #13b6b4;
}

.chatMain {
  width: 100%;
}

.chatMain-inner {
  margin-left: 300px;
  min-width: 500px;
  height: 100%;
  word-break: break-all;
}

.chatLeftHead {
  width: 100%;
  height: 170px;
  padding: 16px 16px 10px 16px;
}

.contactListBox {
  height: 45px;
  width: 100%;
  color: #ffffff;
}

.contactListBox ul {
  padding-left: 0px;
}

.contactListBox ul li {
  float: left;
  width: 35%;
  height: 30px;
  list-style-type: none;
}

.contactListBox ul li a {
  color: #ffffff;
}

.schoolData_className {
  text-decoration: none !important;
  color: rgba(254, 254, 255, 1) !important;
}

.schoolData_classImg {
  margin-top: 7px;
}

.schoolData_studentName {
  height: 50px;
  background-color: #13b6b4;
  border-bottom: 1px solid rgba(3, 154, 160, 0.5);
  padding-top: 10px;
}

.centerShow {
  text-align: center;
  padding-top: 20%;
}

.font-color-white10 {
  color: RGBA(255, 255, 255, 1);
}

.font-color-white7 {
  color: RGBA(255, 255, 255, 0.75);
}

.font-color-white8 {
  color: RGBA(255, 255, 255, 0.85);
}

.box1 {
  float: left;
  width: 300px;
  height: 100%;
  background: #13b6b4;
}

.box2 {
  margin-left: 300px;
  height: 100%;
}

.selectGroup {
  background-color: rgba(221, 221, 221, 0.45);
}

.classNameHover:hover {
  background-color: rgba(221, 221, 221, 0.45);
}

.selectChat {
  background-color: rgba(196, 219, 220, 0.3);
}

.chatText {
  padding: 3px 8px;
  background: #10b3b7;
  position: relative;
  margin-right: 18px;
  font-size: 18px;
  border-radius: 7px;
  max-width: 550px;
  color: #ffffff;
}

.chatText span {
  width: 0;
  height: 0;
  overflow: hidden;
  position: absolute;
}

.chatText span.bot {
  border-width: 20px;
  border-style: solid;
  border-color: transparent #ffffff #ffffff transparent;
  left: -40px;
  top: 0;
}

.chatText span.top {
  border-width: 5px 5px;
  border-style: dashed solid solid dashed;
  border-color: #10b3b7 #ffffff #ffffff #10b3b7;
  right: -10px;
  top: 10px;
}

.chatText2 {
  padding: 7px 10px;
  margin-left: 20px;
  background: #eeeeee;
  position: relative;
  margin-right: 25px;
  font-size: 18px;
  border-radius: 7px;
  max-width: 550px;
  color: #4b535b;
}

.chatText2 span {
  width: 0;
  height: 0;
  overflow: hidden;
  position: absolute;
}

.chatText2 span.bot {
  border-width: 20px;
  border-style: solid;
  border-color: transparent #ffffff #ffffff transparent;
  left: -40px;
  top: 40px;
}

.chatText2 span.top {
  border-width: 5px 5px;
  border-style: dashed solid solid dashed;
  border-color: #ffffff #eeeeee #eeeeee #ffffff;
  left: -10px;
  top: 10px;
}

.searchStu::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.searchStu:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: rgba(255, 255, 255, 0.6) !important;
}

.searchStu::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: rgba(255, 255, 255, 0.6) !important;
}

.searchStu:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: rgba(255, 255, 255, 0.6) !important;
}

.scrollbar::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 8px;
  /* 高宽分别对应横竖滚动条的尺寸 */
  height: 1px;
}

.scrollbar::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: RGBA(182, 182, 182, 0.45);
}

.scrollbar::-webkit-scrollbar-track {
  /* 滚动条里面轨道 */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ededed;
}

/* 谷歌浏览器视频取消全屏键 */
video::-webkit-media-controls-fullscreen-button {
  display: none;
}

.newAudio {
  width: 85px;
  height: 25px;
}

.newAudio img {
  width: 16px;
  height: 17px;
}

.newAudio span {
  width: 30px;
  height: 17px;
  display: inline-block;
  font-size: 14px;
  margin-left: 40px;
  margin-top: 2px;
}

/* 页面上的消息提醒 */
.msgTips {
  width: 170px;
  height: 52px;
  background: #fff;
  position: fixed;
  bottom: 70px;
  right: 24px;
  border-radius: 8px;
  padding: 8px 8px;
  box-sizing: border-box;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
  overflow: hidden;
  cursor: pointer;
  /*display:none;*/
}

.msgTipsImg {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  float: left;
  background-color: pink;
}

.msgTipsUser {
  width: 83px;
  height: 36px;
  float: left;
  line-height: 36px;
  color: #111c1c;
  font-size: 16px;
  overflow: hidden;
  word-break: keep-all;
  text-overflow: ellipsis;
  padding: 0 8px;
  box-sizing: border-box;
  cursor: pointer;
}
.msgTipsNum {
  float: left;
  background-color: #fe5458;
  color: #fff;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  text-align: center;
  margin-top: 8px;
  font-style: normal;
}

/* 聊天页班级群聊下拉标题的样式 */
.className {
  display: block;
  cursor: pointer;
}

.childrenListHover {
  background-color: #10b3b7;
}

.childrenListHover:hover {
  background: rgba(150, 200, 200, 0.5);
}

.wf-main .newList {
  position: static;
  display: inline-block;
}

.otherShow {
  margin-top: 10px;
  margin-bottom: 10px;
  height: 40px;
  width: 90%;
  float: left;
}

.width_50 {
  width: 50%;
  height: 35px;
}

.drdpError {
  border-color: #f5201c !important;
}

.boderDeepen {
  border-bottom: 1px solid #d0d0d0 !important;
  border-left: 1px solid #d0d0d0 !important;
}

.add-padding-t-120 {
  padding-top: 120px;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-align-justify {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-align-start {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-space-between {
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-font {
  /* font-family: "HelveticaNeueLTPro-Roman"; */
  font-weight: normal;
}

.text-bolder {
  /* font-family: "HelveticaNeueLTPro-Roman;"; */
  font-weight: 600 !important;
}

.clear-padding {
  padding: 0 !important;
}

.clear-margin {
  margin: 0;
}

.ban-textarea {
  resize: none;
}

.btnText {
  color: rgba(88, 102, 110, 1);
}

.headerNeck {
  /* height: 50px!important; */
  line-height: 60px;
  padding-left: 36px;
  padding-right: 0;
}

.text-dker {
  color: #444444;
}

.des-div {
  height: 40px;
  line-height: 40px;
}

.add-width-05 {
  width: 5%;
}

.add-width-08 {
  width: 8%;
}

.add-width-1 {
  width: 10%;
}

.add-width-15 {
  width: 15%;
}

.add-width-2 {
  width: 20%;
}

.add-width-25 {
  width: 25%;
}

.add-width-3 {
  width: 30%;
}

.add-width-33 {
  width: 33%;
}

.add-width-35 {
  width: 35%;
}

.add-width-4 {
  width: 40%;
}

.add-width-48 {
  width: 48%;
}

.add-width-48-import {
    width: 48% !important;
}

.add-width-5 {
  width: 50%;
}

.add-width-6 {
  width: 60%;
}

.add-width-7 {
  width: 70%;
}

.add-width-8 {
  width: 80%;
}

.add-width-9 {
  width: 90% !important;
}

.add-width-100-import {
    width: 100% !important;
}

.add-width-fit-content {
    width: fit-content;
}

.add-inline-block {
  display: inline-block;
}

.reviewTable {
  /* height: 46px; */
  padding: 2px 0px;
  /* line-height: 46px; */
  border-bottom: 1px solid #ebeef5;
}

.reviewLine {
  padding: 14px 0;
}

.approveImg {
  margin-top: 5px;
  /* margin-left: 5px; */
}

.approveImg:hover {
  width: 85px;
  margin-top: 2.5px;
  /* margin-bottom: 2.5px; */
}

.approveImg:active {
  width: 75px;
  margin-top: 7.5px;
}

.clickApproveImg {
  width: 75px;
}

.approveImgBody {
  width: 90px;
  height: 90px;
  margin: 0 auto;
  display: inline-block;
  vertical-align: top;
}

video::-webkit-media-controls-mute-button {
  display: none !important;
}

.changeTime {
  height: 33px;
  margin: 0 15px 0 10px;
}

.changeTime .el-input__icon {
  line-height: 33px;
}

.changeTime .el-input,
.changeTime .el-input__inner {
  height: 33px;
}

.titleText {
  font-family: "SourceSansPro-Semibold", "Source Sans Pro Semibold",
    "Source Sans Pro";
  font-weight: 700;
  font-style: normal;
  text-align: left;
}

.infoText {
  font-size: 24px;
  color: #2b2b2b;
}

.infoText2 {
  font-size: 19px;
  color: #2b2b2b;
}

.infoText3 {
  font-size: 18px;
  color: rgba(41, 41, 41, 0.6);
}

.infoPrepared {
  font-size: 22px;
  color: #f97c00;
}

.white-background {
  background-color: #ffffff;
}

.ratingPeriodNextBtn {
  color: #ffffff;
  overflow: hidden;
  position: fixed;
  bottom: 18px;
  right: 184px;
}

.ratingPeriodNextBtn2 {
  overflow: hidden;
  position: fixed;
  bottom: 18px;
  right: 300px;
}

.periodViewTitleBox {
  height: 72px;
  position: absolute;
  top: 0;
  width: 100%;
}

.periodViewBodyBox {
  position: absolute;
  width: 100%;
  top: 72px;
  bottom: 60px;
  overflow: auto;
}

.periodViewBodyBox2 {
  position: absolute;
  width: 100%;
  top: 72px;
  overflow: auto;
}

.periodViewFootBox {
  height: 60px;
  width: 100%;
  position: absolute;
  bottom: 0;
}

.text-black-bolder {
  color: black;
  font-weight: bolder;
}

.display-inline-block {
  display: inline-block;
}

.defalut-radio {
  width: 22px;
  height: 22px;
  border-radius: 11px;
  border: 1px solid #dde6e8;
  background: #fff;
}

.select-radio-view {
  width: 22px;
  height: 22px;
  border-radius: 11px;
  border: 1px solid #dde6e8;
  background: #ffffff;
}

.select-radio {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  border: 1px solid #dde6e8;
  margin-top: 4px;
  margin-left: 4px;
  background: #10b3b7;
}

.rating-period-preview {
  padding: 14px 20px;
}

.period-title-no-class {
  height: 45px;
  line-height: 45px;
  background: #ffefdd;
  text-align: center;
  color: #f14300;
  font-weight: 500;
}

.period-title-close {
  position: absolute;
  right: 20px;
  font-size: 20px;
  top: 21px;
}

.period-select-class-name {
  display: inline-block;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.headerEvent {
  height: 6px;
  width: 6px;
  background: #fa6666;
  border-radius: 5px;
  display: inline-block;
  position: relative;
  top: -6px;
}

.add-margin-t-90 {
  margin-top: 90px;
}

.align-top {
  vertical-align: top;
}

.table-name div {
  word-break: keep-all !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
}

.line-feed {
  word-break: keep-all !important;
  word-wrap: break-word !important;
  white-space: pre-wrap !important;
}

.error_pending {
  background-color: #f0f9ec !important;
}

.error_ignore {
  /* color: #A8ABB2 !important; */
}

.red-point {
  margin-left: 5px;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  display: inline-block;
  text-align: center;
  font-size: 0.1em;
  /* padding:1px; */
  color: #fff;
  background: #f56c6c;
}

.word_keep-all {
  word-break: keep-all;
}

.word_overflow_hideen {
  line-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.myScroll-y {
  overflow-y: auto;
}

.myScroll-x {
  overflow-x: auto;
}

header {
  z-index: 1000;
}

.separate {
  height: 1px;
  width: 100%;
  background-color: #ebeef5;
}

.separate {
  height: 1px;
  width: 100%;
  background-color: #ebeef5;
}

.confirm-dialog .el-dialog__title {
  font-size: 22px !important;
  font-weight: bold !important;
}

.confirm-dialog .el-dialog__body {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  font-size: 16px !important;
}

.no-header-dialog .el-dialog__header {
  display: none;
}

.survey-dialog {
  box-shadow: 0 5px 15px rgb(0 0 0 / 50%);
}

.survey-dialog > .el-dialog__body {
  padding: 0;
  height: calc(100% - 120px);
}

.sure-close-dialog {
  box-shadow: 0 5px 15px rgb(0 0 0 / 50%);
}

.sure-close-dialog > .el-dialog__header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}

.sure-close-dialog > .el-dialog__body {
  position: relative;
  padding: 15px;
  color: #606266;
  word-break: break-all;
}

.sure-close-dialog > .el-dialog__footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}

.submit_success {
  background-color: #dff0d8;
  width: 100%;
  align-items: center;
  justify-content: center;
}

.submit_success.el-notification.right {
  right: 0;
}

.submit_success .el-notification__title {
  font-size: 20px;
  color: #3c763d;
  margin: 10px 0;
  font-weight: 500;
}

.upload-item .el-form-item .el-form-item__content {
  margin-left: 0 !important;
}

.el-form-item__error {
  padding-left: 12px;
  padding-top: 0px;
  font-size: 14px;
}

.arm-to-archive-modal .el-message-box__message {
  font-size: 15px !important;
}

.arm-to-archive-modal .el-message-box__btns span {
  font-size: 14px !important;
}

.pleaseSign-parent {
  margin-left: 10px;
  width: 170px;
  height: 70px;
  border: 2px dashed #d7d7d7;
  text-align: center;
  line-height: 70px;
  color: #d7d7d7;
  border-radius: 5px;
  vertical-align: top;
  flex: none;
}

.sign-parent {
  background-color: #f5f5f5;
  margin-left: 10px;
  width: 170px;
  height: 70px;
  text-align: center;
  line-height: 70px;
  color: #d7d7d7;
  border-radius: 5px;
  vertical-align: top;
  flex: none;
}

.full-height-popover {
  max-height: 100%;
  overflow-y: auto;
}

/* 自定义 el-switch  */
.defineSwitch .el-switch__label {
  position: absolute;
  display: none;
  color: #fff;
}

/*打开时文字位置设置*/
.defineSwitch .el-switch__label--right {
  z-index: 1;
  left: -6px;
}

/*关闭时文字位置设置*/
.defineSwitch .el-switch__label--left {
  z-index: 1;
  right: -30px;
}

/*显示文字*/
.defineSwitch .el-switch__label.is-active {
  display: block;
}

/*开关按钮的宽度*/
.defineSwitch.el-switch .el-switch__core,
.el-switch .el-switch__label {
  width: 43px !important;
}

.curriculum-unit-detail-center h1,
.curriculum-unit-detail-center h2,
.curriculum-unit-detail-center h3,
.curriculum-unit-detail-center h4,
.curriculum-unit-detail-center h5,
.curriculum-unit-detail-center h6 {
  line-height: 1.42;
}

.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  line-height: 1.42;
}

.lesson-delete-confirm-btn {
  background-color: #f56e6e !important;
  border-color: #f56e6e !important;
}

.font-family-psc {
  font-family: "PingFang SC", "Source Sans Pro", "Helvetica Neue", Helvetica,
    Arial, sans-serif;
}

.font-weight-semibold {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

.font-color-primary {
  color: #10b3b7;
}

.lg-scroll-y {
  overflow-y: scroll;          /* 启用纵向滚动 */
}

.lg-scroll-y::-webkit-scrollbar {
  width: 6px;
  max-height: 154px;
}

.lg-scroll-y::-webkit-scrollbar-track {
  background: transparent;
}

.lg-scroll-y::-webkit-scrollbar-thumb {
  background: #DCDFE6;
  border-radius: 3px;
}

.divide-line-gray {
  background: #eee;
  height: 1px;
  width: 100%;
}

.add-margin-l-3 {
  margin-left: 3px;
  display: inline-flex;
}

.inline-flex-row-vertical-center {
  display: inline-flex;
  align-items: center;
  flex-direction: row;
}

.inline-flex-column-center {
  display: inline-flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}

.flex-row-vertical-center {
  display: flex;
  align-items: center;
  flex-direction: row;
}

/* 播放dll语音的样式 */
.show-play-icon {
  width: 20px;
  height: 20px;
  background: url("../img/dll/icon_voice.png");
  margin-right: 3px;
}

.voice-playing {
  width: 20px;
  height: 20px;
  background: url("../img/dll/voice_playing.png");
  margin-right: 3px;
}

.voice-playing {
  width: 20px;
  height: 20px;
  background: url("../img/dll/voice_playing.png");
  -webkit-animation: playVoice steps(3, end) 1s infinite;
  animation: playVoice steps(3, end) 1s infinite;
}

@-webkit-keyframes playVoice {
  100% {
    background-position: -60px 0;
  }
}
/* 播放dll语音的样式 */
.show-play-icon-new {
  width: 24px;
  height: 24px;
  background: url("../img/dll/icon_voice_new.svg");
  margin-right: 3px;
}

.voice-playing-new {
  width: 24px;
  height: 24px;
  background: url("../img/dll/voice_playing_new.png");
  -webkit-animation: playVoice steps(3, end) 1s infinite;
  animation: playVoiceNew steps(3, end) 1s infinite;
}

@-webkit-keyframes playVoiceNew {
  100% {
    background-position: -72px 0;
  }
}

.add-border-solid-gray {
  border: 1px solid #eee;
}

.add-border-t-solid-gray {
  border-top: 1px solid #eee;
}

.add-border-b-solid-gray {
  border-bottom: 1px solid #eee;
}

.flex-row-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.flex-row-between-reverse {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
}

.text-title-18 {
  font-size: 18px;
  color: #303133;
}

.flex-1 {
  flex: 1;
}

.flex-2 {
  flex: 2;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  display: -webkit-flex;
  align-items: center;
}

.flex-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.line-height-1-multiple {
  line-height: 1;
}

.font-size-17 {
  font-size: 17px;
}

.add-margin-l-5 {
  margin-left: 5px;
}

.add-margin-10 {
  margin: 10px;
}

.add-padding-lr-10 {
  padding-left: 10px;
  padding-right: 10px;
}

.add-padding-lr-12 {
  padding-left: 12px;
  padding-right: 12px;
}

.width-90-percent {
  width: 90%;
}

.max-width-80-percent {
  max-width: 80%;
}

.max-width-60-percent {
  max-width: 60%;
}


.max-width-40-percent {
  max-width: 40%;
}

.max-width-200 {
  max-width: 200px !important;
}

.max-width-300 {
  max-width: 300px !important;
}

.max-width-400 {
  max-width: 400px !important;
}

.max-width-500 {
  max-width: 500px !important;
}

.max-width-600 {
  max-width: 600px !important;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-absolute-bottom-0 {
  bottom: 0;
}

.position-absolute-right-0 {
  right: 0;
}

.position-absolute-top-0 {
  top: 0;
}

.position-absolute-left-0 {
  left: 0;
}

.position-absolute-top-4 {
  top: 4px;
}

.text-error-color {
  color: #f56c6c;
}
.text-warn-color {
  color: #FE9E46;
}
.add-margin-t-5 {
  margin-top: 5px;
}

.flex-row-right {
  display: flex;
  justify-content: flex-end;
}

.add-padding-tb-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.add-padding-tb-6 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.add-padding-tb-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.add-padding-lr-16 {
  padding-left: 16px;
  padding-right: 16px;
}

.add-padding-lr-18 {
  padding-left: 18px;
  padding-right: 18px;
}

.add-padding-lr-20 {
  padding-left: 20px;
  padding-right: 20px;
}

.add-padding-lr-24 {
    padding-left: 24px;
    padding-right: 24px;
}

.add-padding-tb-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.add-padding-tb-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.dll-statistics-title-back-info {
  width: 100%;
  height: 50px;
  position: relative;
  text-align: center;
  background: #f0f3f4;
}

.dll-statistics-title {
  color: #111c1c;
  font-size: 18px;
  line-height: 50px;
  vertical-align: center;
}

.dll-statistics-detail-goback {
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 21.59px;
  height: 18px;
  cursor: pointer;
}

.tag-default {
    background: #F4F4F5;
    border-radius: 35px;
    padding: 4px 16px;
}

.height-20 {
  height: 20px;
}

.width-20 {
  width: 20px;
}

.height-48 {
  height: 48px;
}

.height-50 {
  height: 50px;
}

.height-80 {
  height: 80px;
}

.height-190 {
  height: 190px;
}

.height-200 {
  height: 200px;
}

.el-icon-question-color {
  color: #909399;
}

.lg-pa-15 {
  padding: 15px;
}

.lg-pa-8 {
  padding: 8px;
}

.dll-text-title-alt-color {
  color: #111C1C;
}

.dll-text-title-color {
  color: #303133;
}

.single-line {
  max-lines: 1;
}

.height-100 {
  height: 100px;
}

.height-300 {
  height: 300px;
}

.height-400 {
  height: 400px;
}

.divide-gap-theme-background {
  background: #f0f3f4;
  height: 10px;
  width: 100%;
}

.lg-pa-6 {
  padding: 6px;
}

.add-margin-6 {
  margin: 6px;
}
.width-30 {
  width: 30px;
}
.width-40 {
  width: 40px;
}

.height-32 {
    height: 32px;
}

.height-40 {
  height: 40px;
}

.position-absolute-top-half {
  top: 50%;
}

.margin-auto {
  margin: auto;
}

.position-absolute-0 {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.display-inline-flex {
  display: inline-flex !important;
}

.add-margin-t-20 {
  margin-top: 20px;
}

.add-margin-lr-10 {
  margin-left: 10px;
  margin-right: 10px;
}

.add-margin-lr-16 {
  margin-left: 16px;
  margin-right: 16px;
}

.add-margin-b-0 {
  margin-bottom: 0;
}

.add-margin-r-5 {
  margin-right: 5px;
}

.add-margin-b-3 {
  margin-bottom: 3px;
}

.theme-background {
  background: #e2edef;
}

.red-dot-tip {
  border-radius: 10px;
  background: #f66b6d;
  width: 10px;
  height: 10px;
  position: absolute;
  right: -10px;
  top: 9px;
}

.img-cover {
  object-fit: cover;
}

.pre-wrap {
  white-space: pre-wrap;
}

.justify-content-end {
  -webkit-justify-content: flex-end;
  -moz-justify-content: flex-end;
  justify-content: flex-end;
}

.add-margin-t-14 {
  margin-top: 14px;
}

.align-self-center {
  align-self: center;
}

.border-radius-6 {
  border-radius: 6px;
}

.border-radius-14 {
    border-radius: 14px;
}

.border-radius-16 {
    border-radius: 16px;
}

.box-shadow-3 {
  box-shadow: 1px 1px 3px #e4e7ed;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-flex-start {
   align-items: flex-start;
}

.lg-pa-20 {
  padding: 20px;
}

.text-title-alt-color {
  color: #606266;
}

.text-title-color {
  color: #303133;
}

.title-bg-color {
  background-color: #f5f9f8;
}

.lg-pa-16 {
  padding: 16px;
}

.border-radius-4 {
  border-radius: 4px;
}

.margin-right-auto {
  margin-right: auto;
}

.add-margin-l-2 {
  margin-left: 2px;
}

.add-margin-l-0 {
  margin-left: 0;
}

.height-30 {
  height: 30px;
}

.height-36 {
  height: 36px;
}

.align-self-end {
  align-self: flex-end;
}

.area-disabled {
  pointer-events: none;
}

.area-cannot-click {
  pointer-events: none;
  cursor: no-drop;
}

.add-padding-t-5 {
  padding-top: 5px;
}

.list-hover-bg-color {
  background-color: #eaeef6;
}

.list-active-bg-color {
  background-color: #def3f2;
}

.background-primary {
  background-color: #10b3b7;
}

.child-item-active {
  color: #10b3b7;
  background-color: #def3f2;
}

.width-3 {
  width: 3px;
}

.add-margin-tb-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.border-dashed-line {
  border: 1px dashed;
}

.no-border {
  border: none;
}

.color-text-danger {
  color: #f56c6c;
}

.bg-gray {
  background-color: #ebeef5;
}

.min-width-30 {
  min-width: 30px;
}

.red-new-tip {
  border-radius: 10px;
  width: 34px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  position: absolute;
  right: -34px;
  top: 9px;
  color: white;
  font-size: 12px;
  background-color: #f66b6d;
}

.add-margin-lr-32 {
  margin-left: 32px;
  margin-right: 32px;
}

.border-radius-3 {
  border-radius: 3px;
}

.height-0 {
  height: 0;
}

.width-50 {
  width: 50px;
}

.min-width-40 {
  min-width: 40px;
}

.color-dll-title {
  color: #676879;
}

.color-323338 {
  color: #323338 !important;
}
.color-999999 {
  color: #999999 !important;
}

.color-676879 {
  color: #676879 !important;
}

.color-icon {
  color: #676879;
}

.box-shadow-8 {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.1);
}

.add-border-top-radius-4 {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.add-border-bottom-radius-4 {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.font-weight-normal {
  font-weight: normal;
}
.font-weight-400 {
  font-weight: 400;
}
.font-400-important {
  font-weight: 400 !important;
}
.font-weight-100 {
    font-weight: 100;
}
.bg-white {
  background-color: #ffffff;
}
.bg-color-F5F6F8 {
  background-color: #f5f6f8;
}
.light-primary-bg {
  background: #ddf2f3;
}
.lg-card {
  background: #ffffff;
  /* 卡片投影 */
  border-radius: 8px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.101025);
}
.lg-img-thumbnail {
  display: inline-block;
  width: 100px;
  height: 100px;
  object-fit: cover;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
/* input输入框占位符  */
.el-input__inner::placeholder {
  font-weight: 400 !important;
}

.no-record-img {
  width: 300px;
  object-fit: contain;
  height: 200px;
}


/* 档案袋风格 */
.lg-tabs-archives {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    align-content: center;
}

.lg-tabs-archives .lg-tabs-archives-item {
    height: 40px;
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    cursor: pointer;
    z-index: 99;
}
.app .lg-tabs-archives .lg-tabs-archives-item.selected {
    z-index: 150;
}
.lg-tabs-archives .lg-tabs-archives-item:nth-child(2) {
  z-index: 120;
}
.lg-tabs-archives .lg-tabs-archives-item:nth-child(3) {
  z-index: 110;
}
.lg-tabs-archives .lg-tabs-archives-item:nth-child(4) {
  z-index: 100;
}
.lg-tabs-archives-item:first-child .lg-tabs-archives-item-content {
    border-top-left-radius: 8px;
}
.lg-tabs-archives-item .lg-tabs-archives-item-content {
    display: inline-block;
    vertical-align: top;
    height: 40px;
    font-size: 16px;
    line-height: 24px;
    padding: 8px 20px;
    color: var(--color-text-secondary);
    background-color: var(--color-gray-white) !important;
}

.lg-tabs-archives
    .lg-tabs-archives-item.selected:first-child
    .lg-tabs-archives-item-content {
    box-shadow: -2px -2px 8px -2px rgba(0, 0, 0, 0.101025);
}
.lg-tabs-archives-item.selected .lg-tabs-archives-item-content {
    color: var(--color-primary) !important;
    box-shadow: 0px -2px 8px -2px rgba(0, 0, 0, 0.101025);
    background-color: var(--color-white) !important;
    font-weight: 600;
}
.lg-tabs-archives .lg-tabs-archives-item:not(:first-child).selected:before {
    background-image: url(../img/us/white_left.png);
    z-index: 102;
}

.lg-tabs-archives .lg-tabs-archives-item.selected:after {
    background-image: url(../img/us/white_right.png);
    z-index: 102;
}

.lg-tabs-archives .lg-tabs-archives-item:not(:first-child):before {
    display: inline-block;
    vertical-align: top;
    content: "";
    width: 30px;
    height: 100%;
    background-image: url(../img/us/gray_left.png);
    background-position: 100%;
    background-size: cover;
    position: absolute;
    left: -25px;
    z-index: 100;
}

.lg-tabs-archives .lg-tabs-archives-item:after {
    display: inline-block;
    vertical-align: top;
    content: "";
    width: 25px;
    height: 100%;
    background-image: url(../img/us/gray_right.png);
    background-position: 0;
    background-size: cover;
    z-index: 100;
}
.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.btn-center {
    display: flex;
    align-items: center;
    gap: 4px;
}
.gap-y-12 {
  row-gap: 12px;
}
.gap-x-24 {
  column-gap: 24px;
}
.gap-x-36 {
  column-gap: 36px;
}
.gap-4 {
  gap: 4px;
}
.gap-3 {
  gap: 3px;
}

.gap-8 {
    gap: 8px;
}

.gap-10 {
    gap: 10px;
}

.gap-12 {
  gap: 12px;
}

.gap-16 {
    gap: 16px;
}

.gap-24 {
  gap: 24px;
}

.gap-36 {
  gap: 36px;
}
.vertical-middle {
  vertical-align: middle;
}
.lg-upload-file-input input[type=file]::file-selector-button {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--color-text-placeholder);
    border: 0;
    width: 102px;
    height: 32px;
    background: var(--color-gray-white);
    border-radius: 4px;
}

.lg-upload-file-input input[type=file] {
    border: 2px solid var(--color-border);
    border-radius: 4px 0px 0px 4px;
    padding: 2px;
    color: var(--color-text-placeholder);
}

.lg-upload-file-input input[type=file]:focus {
    border-color: var(--color-primary);
}

.lg-upload-file-input .upload-file-right-btn {
    border-radius: 0 4px 4px 0;
}

.lg-upload-file-input input[type=file]:focus, input[type=checkbox]:focus, input[type=radio]:focus {
    outline: unset;
    outline-offset: 0;
}

.lg-notify{
  position:relative
}

.lg-notify::after {
  content: "";
  width: 6px !important;
  height: 6px !important;
  border-radius: 50%;
  background: #F56C6C;
  display: inline-block;
  position: absolute;
  top: 9px;
  right: 4px;
  border-radius: 50%;
}
.overflow-x-hidden {
    overflow-x: hidden;
}
.min-width-0 {
    min-width: 0;
}
.border-radius-half {
    border-radius: 50%;
}
.lg-icon-date-gray {
    font-weight: 400 !important;
}
.overflow-y-hidden {
    overflow-y: hidden;
}

.lg-text-ellipsis-limit-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -o-text-overflow: ellipsis;
  -moz-text-overflow: ellipsis;
  -webkit-text-overflow: ellipsis;
}

.margin-left-auto {
  margin-left: auto;
}

.lg-color-dropdown-unselected {
  color: var(--color-text-placeholder);
}
.signature-img-size {
    width: 115px;
    height: 50px;
}
.overflow-y-auto{
    overflow-y: auto;
}

.height-60 {
    height: 60px;
}

.divider-vertical {
    width: 1px;
    background-color: var(--color-inner-dashed-border);
}
.max-height-400 {
    max-height: 400px;
}

.border-1px-dcdfe6 {
  border: 1px solid #dcdfe6;
}

.display-grid {
    display: grid;
}

.grid-colum-2 {
    grid-template-columns: repeat(2, 1fr);
}
.max-w-33 {
    max-width: 33%;
}
.max-w-100 {
    max-width: 100%;
}
.max-w-150px {
    max-width: 150px;
}
.color-red {
  color: red;
}
/* 隐藏所有带有 data-mjx-err 属性的元素 */
[data-mjx-error] {
    display: none;
}

/* 为 tab 添加动画效果 */
.el-tabs__content {
  /* 为 tab 内容区域添加 0.3s 的平滑过渡效果 */
  transition: all 0.3s ease;
}

.el-tabs__item {
  /* 为 tab 标签项添加 0.2s 的缓动过渡效果 */
  transition: all 0.2s ease-in-out; 
}

.el-tabs__active-bar {
  /* 为 tab 下方的活动条添加 0.3s 的贝塞尔曲线过渡效果,使动画更加流畅自然 */
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 按钮悬浮动画效果样式 */
.lg-button-animation {
  /* 添加平滑过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.lg-button-animation:not(.is-disabled):hover {
  /* 添加上浮动画效果 */
  transform: translateY(-2px);
}

.lg-button-animation:not(.is-disabled):active {
  /* 点击时回到原位 */
  transform: translateY(0);
}
.ul-disc-color-white {
  color: #fff;
  list-style-type: disc;
  padding: 2px 20px;
  li {
    list-style-type: disc;
    line-height: 1.5;
  }
  li:not(:first-child) {
    margin-top: 5px;
  }
}
/* 垂直抖动动画 */
@keyframes vertical-shake {
  0% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-4px);
  }
  75% {
    transform: translateY(4px);
  }
  100% {
    transform: translateY(0);
  }
}

@-webkit-keyframes vertical-shake {
  0% {
    -webkit-transform: translateY(0);
  }
  25% {
    -webkit-transform: translateY(-4px);
  }
  75% {
    -webkit-transform: translateY(4px);
  }
  100% {
    -webkit-transform: translateY(0);
  }
}
/* 虚拟列表样式 */
div[role="listitem"].lg-virtual-list-item:not(.virtual-item-0)>ul.el-select-group__wrap {
  padding-top: 24px;
}

div[role="listitem"].lg-virtual-list-item>ul.el-select-group__wrap {
  /* 解决列表分组 title 消失问题 */
  display: block !important;

  .el-select-group__title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

div[role="listitem"].lg-virtual-list-item:not(.virtual-item-0)>ul.el-select-group__wrap::before {
  content: "";
  position: absolute;
  display: block;
  left: 20px;
  right: 20px;
  top: 12px;
  height: 1px;
  background: #e4e7ed;
}
.common-dialog {
  .el-dialog__body {
    padding: 24px;
  }
  .el-dialog__header {
    padding: 24px 24px 0 24px;
  }
  .el-dialog__footer {
    padding: 0 24px 24px 24px;
  }
}
