<template>
    <div class="header relative" :class="{'has-banner': showBanner}">
      <!-- 扩展横幅 -->
      <ExtensionBanner @update:visible="updateBannerVisibility" />
  
      <div class="header-container">
        <!-- 左侧部分 -->
        <div class="header-left">
          <!-- Logo -->
          <img v-if="isLogin" src="/images/auth/logo.png" class="logo-image" />
          <img v-else :src="require('@/assets/cg/images/home/<USER>')" alt="Curriculum Genie"
               @click="handleLogoClick"
               class="logo-image logo-image-large lg-pointer" />
          <!-- PC端菜单 - 只在非移动端显示 -->
          <el-menu
            v-if="!isMobile"
            :default-active="activeMenu"
            :ellipsis="false"
            mode="horizontal"
            @select="handleSelectMenu"
            class="header-menu"
            style="min-width: 100%;"
          >
            <el-menu-item index="/curriculum-genie/unit-planner">Unit Planner</el-menu-item>
            <el-menu-item index="/lessons">Lesson Plan</el-menu-item>
          </el-menu>
        </div>
  
        <!-- 中间部分 - 移动端为空，不显示菜单 -->
        <div v-if="isMobile" class="header-middle"></div>
  
        <!-- 右侧部分 -->
        <div class="header-right">
          <!-- <client-only> -->
  
            <!-- 已登录则显示头像 -->
            <div v-if="currentUser" class="user-avatar-container lg-pointer">
              <!-- Unit Planner 入口 - 仅桌面端显示 -->
              <!-- <el-button
                v-if="!isUnitPlanner && !isLessonPlanner && !isMobile"
                round
                type="primary"
                size="large"
                class="unit-planner-btn"
                @click="openUnitPlanner()"
              >
                Unit Planner
              </el-button> -->
               <!-- 头像 - 桌面端点击跳转个人中心，移动端点击打开菜单 -->
               <el-avatar v-if="currentUser && currentUser.avatar_url"
                         :src="currentUser.avatar_url"
                         @click.native="isMobile ? showMobileMenu = true : openUserProfile()"/>
               <el-avatar v-else
                 :src="require('@/assets/cg/images/auth/default-avatar.png')"
                 @click.native="isMobile ? showMobileMenu = true : openUserProfile()"/>
            </div>
  
            <!-- 下载 - 仅桌面端显示 -->
            <a v-if="!hasPlugin && !currentUser && !isLogin && !isMobile" @click="$analytics.sendEvent('cg_gotogoogle_install')" href="https://chromewebstore.google.com/detail/curriculum-genie/jbhknkdnmmcipjcfjngiojllgbgolmao" target="_blank">
              <el-button class="chrome-btn" type="primary" plain size="large" round>
                <span>Add to Chrome for free</span>
              </el-button>
            </a>
               <!-- 登录 -->
           <!-- <el-button class="login-btn" v-if="!currentUser && !isLogin" type="primary" round size="large"  @click="openLoginDialog">
            <span>Sign up/Sign in</span>
            </el-button> -->
  
            <!-- 移动端未登录时的菜单按钮 -->
            <!-- <el-button v-if="!currentUser && !isLogin && isMobile" @click="showMobileMenu = true" text class="mobile-menu-btn">
              <el-icon><ElIconMenu /></el-icon>
            </el-button> -->
          <!-- </client-only> -->
        </div>
      </div>
  
      <!-- 移动端菜单抽屉 -->
      <el-drawer
        id="mobile-menu"
        :visible.sync="showMobileMenu"
        direction="rtl"
        :append-to-body="true"
        size="80%"
        :with-header="true"
      >
        <div class="mobile-menu">
          <div class="user-info" v-if="currentUser">
            <el-avatar src="/images/auth/default-avatar.png" :size="50" class="user-avatar" />
            <p class="user-email">{{ currentUser.email }}</p>
          </div>
  
          <div class="menu-items">
            <!-- 如果不在特定页面，显示主页菜单项 -->
            <div v-if="!isLogin && !isUserProfile && !isUnitPlanner && !isLessonPlanner"
                 class="menu-item"
                 @click="handleMobileMenuSelect('/')">
              Home
            </div>
            <div v-if="!isLogin && !isUserProfile && !isUnitPlanner && !isLessonPlanner"
                 class="menu-item"
                 @click="handleMobileMenuSelect('/extension')">
              Extension
            </div>
            
            <!-- Unit Planner 菜单项 -->
            <div class="menu-item"
                 @click="handleMobileMenuSelect('/curriculum-genie/unit-planner')">
              Unit Planner
            </div>
  
            <!-- Lesson Plan 菜单项 -->
            <div class="menu-item"
                 @click="handleMobileMenuSelect('/lessons')">
              Lesson Plan
            </div>
  
            <!-- 用户中心菜单项 -->
            <div v-if="currentUser"
                 class="menu-item"
                 @click="handleMobileMenuSelect('/user-profile')">
                 User Profile
            </div>
  
            <!-- 下载按钮 -->
            <div v-if="!hasPlugin && !currentUser" class="download-container">
              <a @click="$analytics.sendEvent('cg_gotogoogle_install')"
                 href="https://chromewebstore.google.com/detail/curriculum-genie/jbhknkdnmmcipjcfjngiojllgbgolmao"
                 target="_blank">
                <el-button type="primary" class="download-btn">Add to Chrome for free</el-button>
              </a>
            </div>
          </div>
        </div>
      </el-drawer>
      <invitation-dialog ref="invitationDialogRef" :type="eventType" :source="source"/>
      <unit-list-invitation-dialog ref="unitListInvitationDialogRef" :type="eventType"/>
      <posthog-survey ref="posthogSurveyRef" />
      <!-- 添加登录弹窗组件 -->
      <!-- <login-dialog ref="loginDialog" /> -->
    </div>
  </template>
  
<script>
import { mapState, mapActions } from 'vuex'
import { useUserApi } from '@/api/cg/user'
import InvitationDialog from '@/components/cg/invitations/InvitationDialog.vue'
import UnitListInvitationDialog from '@/components/cg/invitations/UnitListInvitationDialog.vue'
import PosthogSurvey from '@/components/cg/posthog-survey/index.vue'
import ExtensionBanner from '@/components/cg/extension/ExtensionBanner.vue'
import { useInvitationApi } from '@/api/cg/invitation'
import { equalsIgnoreCase } from '@/utils/common'
//   import LoginDialog from '~/components/auth/login-dialog.vue'
//   import { useAuthStore } from '@/stores/auth'
//   import { LOGIN_PATH, UNINSTALL_FEEDBACK_PATH, SPECIAL_PATHS, isSpecialPath } from '~/constants/routes'

  
  export default {
    components: {
      InvitationDialog,
      PosthogSurvey,
      UnitListInvitationDialog,
      ExtensionBanner
    },
    data() {
      return {
        // 是否是登录页面
        isLogin: this.$route.path === '/login',
        // 是否是 User Profile 页面
        isUserProfile: this.$route.path === '/user-profile',
        // 是否是 Unit Planner 页面
        isUnitPlanner: this.$route.path.includes('/unit-planner'),
        // 是否是 Lesson Planner 页面
        isLessonPlanner: this.$route.path.includes('/lessons'),
        // 调整大小计时器引用
        resizeTimer: null,
        // 从setup()转换的响应式数据
        showBanner: false,
        showMobileMenu: false,
        isMobile: false,
        eventType: null,
        source: null
      }
    },
    computed: {
      ...mapState({
        currentUser: state => state.cgAuth.user
      }),
      // 选中的菜单项
      activeMenu() {
          const path = this.$route.path
          if (path.includes('/curriculum-genie/unit-planner') || path.includes('/curriculum-genie/unit-adapt')) {
              return '/curriculum-genie/unit-planner'
          } else if (path.includes('/lessons/')) {
            return '/lessons'
          }
          return path
        },
      /**
       * 计算header高度
       */
      headerHeight() {
        return this.showBanner ? 112 : 60
      },
      hasPlugin() {
        return this.$store.state.cgAuth.hasPlugin
      }
    },
    created() {
      this.$bus.$on('message', this.messageHandler)
      // 获取功能引导
      this.$store.dispatch('getUserNeedGuideFeaturesAction')
      this.$store.dispatch('getLessonModuleSettings')
    },
    async mounted() {
      // 监听header高度变化，通知应用调整内容区域padding
      this.$watch('headerHeight', (newHeight) => {
        // 确保只在浏览器环境中执行
        if (typeof document === 'undefined') return;
  
        document.documentElement.style.setProperty('--header-height', `${newHeight}px`)
      }, { immediate: true })
  
      // 在页面加载完成后计算移动端横幅高度
      if (typeof window !== 'undefined') {
        // 页面加载完成后检查横幅
        window.addEventListener('load', () => {
          if (this.showBanner) {
            this.calculateMobileBannerHeight();
          }
        });
  
        // 添加窗口大小变化监听器
        window.addEventListener('resize', () => {
          if (this.showBanner) {
            // 使用节流函数避免频繁计算
            if (this.resizeTimer) {
              clearTimeout(this.resizeTimer);
            }
            this.resizeTimer = setTimeout(() => {
              this.calculateMobileBannerHeight();
            }, 150);
          }
          this.checkMobile()
        });
      }
      
      // 检测是否是移动设备
      this.checkMobile()

      this.$analytics.initPostHog(this.currentUser)
    },
    methods: {
      ...mapActions('cgShareLink', ['refreshShareLink']),
      messageHandler(evt) {
        if (evt) {
          // 事件类型
          let event = null
          // 如果事件类型为字符串，则转换为大写
          if (typeof evt === 'string') {
            event = evt.trim().toUpperCase()
          } else {
            event = evt.event
          }
          // 是否是 Unit Planner 页面
          const isUnitPlanner = this.$route.path === '/unit-planner'
          // 是否是 Lesson Planner 页面
          const isLessonPlanner = this.$route.path === '/lesson-plan'
          // 根据类型做不同的处理
          switch (event) {
            case 'GO_TO_SHARE_PAGE':
              // 事件类型
              let eventType = evt.type
              // 来源
              let source = evt.source
              // 根据类型，打开不同弹窗
              if (eventType) {
                eventType = eventType.trim().toUpperCase()
                if (eventType === 'UNIT_LIST_INVITE_MORE') {
                  this.openUnitListInvitationDialog(eventType)
                } else if (eventType === 'UNIT_COMPLETED_INVITE_SHARE_TO_FACEBOOK') {
                  // 课程完成分享到 Facebook 的按钮事件
                  const invitationDialog = this.$refs.invitationDialogRef
                  invitationDialog && invitationDialog.createShareRewardAndToPage('facebook')
                } else if (eventType === 'UNIT_COMPLETED_INVITE_SHARE_TO_TWITTER') {
                  // 课程完成分享到 Twitter 的按钮事件
                  const invitationDialog = this.$refs.invitationDialogRef
                  invitationDialog && invitationDialog.createShareRewardAndToPage('twitter')
                } else {
                  // 所有类型都使用邀请弹窗
                  this.openInvitationDialog(eventType, source)
                }
              }
              break
            case 'GO_TO_CONTACT_PAGE':
              // 新窗口打开，路径为 /contact-form
              window.open('/contact-form', '_blank')
              break
            case 'GET_CURRENT_PLUGIN_USER_INFO':
              // 发送用户信息到 iframe
              sendUserInfoToIframe(this.currentUser, true)
              break
            case 'CLOSE_DIALOG':
              // 事件类型
              let closeEventType = evt.type
              // 根据类型，关闭不同弹窗
              if (closeEventType) {
                closeEventType = closeEventType.trim().toUpperCase()
                if (closeEventType === 'UNIT_LIST_INVITE_MORE') {
                  this.closeUnitListInvitationDialog()
                }
              }
              break
            case 'SHOW_POSTHOG_SURVEY':
              const posthogSurveyRef = this.$refs.posthogSurveyRef
              const unitId = evt.unitId
              posthogSurveyRef && posthogSurveyRef.open(unitId)
              break
            case 'UPDATE_META':
              const meta = evt.meta
              // 转换为期望的格式
              const ogMeta = this.convertToOpenGraphMeta(meta)
              this.updateMeta(ogMeta)
              break
            default:
              break
          }
        }
      },

      /**
       * 更新 meta 数据
       * @param {any} newMeta - 新的 meta 数据
       */
       async updateMeta(newMeta) {
        try {
          const metaToSave = {}
          const metas = Array.isArray(newMeta) ? newMeta : [newMeta]
          metas.forEach(meta => {
            if (meta.property === 'og:type' && meta.content) {
              metaToSave.website = meta.content
            }
            if (meta.property === 'og:title' && meta.content) {
              metaToSave.title = meta.content
            }
            if (meta.property === 'og:description' && meta.content) {
              metaToSave.description = meta.content
            }
            if (meta.property === 'og:image' && meta.content) {
              metaToSave.image = meta.content
            }
            if (meta.property === 'og:site_name' && meta.content) {
              metaToSave.siteName = meta.content
            }
            if (meta.property === 'og:variant' && meta.content) {
              metaToSave.variant = meta.content
            }
          })
          // 调用API保存
          await useUserApi().updateUserMeta(metaToSave)
          // 对于不需要获取插件数量的弹窗，在此触发刷新分享链接
          await this.refreshShareLink()
        } catch (error) {
        }
      },

      /**
       * 转换 meta 数据为 Open Graph 格式
       * @param {any} metaData - 原始 meta 数据
       * @returns {any[]} 转换后的 meta 数据
       */
      convertToOpenGraphMeta(metaData) {
        // 基础 meta 数据
        const meta = [
          { property: 'og:type', content: metaData.website || 'website' },
          { property: 'og:site_name', content: metaData.siteName || 'Learning Genie' }
        ]

        // 添加标题
        if (metaData.title) {
          meta.push({ property: 'og:title', content: metaData.title })
        }

        // 添加描述
        if (metaData.description) {
          meta.push({ property: 'og:description', content: metaData.description })
        }

        // 添加图片
        if (metaData.image) {
          // 从完整URL中提取图片ID
          const imageId = metaData.image.replace('https://s3.amazonaws.com//com.learning-genie.prod.us/', '')
          meta.push({ property: 'og:image', content: imageId })
        }

        // 添加 variant
        if (metaData.variant) {
          meta.push({ property: 'og:variant', content: metaData.variant })
        }

        return meta
      },
      
       /**
       * 打开邀请弹窗
       */
      openInvitationDialog(type, source) {
        this.eventType = type
        this.source = source
        // 确保数据已设置完成再打开弹窗
        this.$nextTick(() => {
          const invitationDialog = this.$refs.invitationDialogRef
          invitationDialog && invitationDialog.open()
        })
      },

      /**
       * 打开单元列表右下角邀请弹窗
       */
      openUnitListInvitationDialog(type) {
        this.eventType = type
        const unitListInvitationDialogRef = this.$refs.unitListInvitationDialogRef
        unitListInvitationDialogRef && unitListInvitationDialogRef.open()
      },

      /**
       * 关闭单元列表右下角邀请弹窗
       */
      closeUnitListInvitationDialog() {
        const unitListInvitationDialogRef = this.$refs.unitListInvitationDialogRef
        unitListInvitationDialogRef && unitListInvitationDialogRef.closeNoAnalyticsEvent()
      },

    /**
     * 向 iframe 发送插件状态消息
     * @param isInstalled 插件是否已安装
     */
     sendPluginStatusToIframe(isInstalled) {
      if (this.showFrame && !this.frameLoading) {
        const iframe = document.querySelector('.iframe-container iframe')
        if (iframe && iframe.contentWindow) {
          const message = {
            event: 'PLUGIN_STATUS_CHANGE',
            isInstalled: isInstalled
          }
          iframe.contentWindow.postMessage(message, '*')
        }
      }
    },

    /**
     * 发送路由变更消息到 iframe
     * @param path 路由路径
     */
    sendRouteChangeMessage(path) {
      const iframe = document.querySelector('.iframe-container iframe')
      if (iframe && iframe.contentWindow) {
        // path 去掉 /
        const message = {
          event: 'PAGE_CHANGE',
          path: path.replace('/', '')
        }
        iframe.contentWindow.postMessage(message, '*')
      }
    },


      /**
       * 检测是否是移动设备
       */
      checkMobile() {
        if (typeof window !== 'undefined') {
          this.isMobile = window.innerWidth <= 768
        }
      },
      
      /**
       * 动态计算移动端下的banner高度并更新header高度
       */
      calculateMobileBannerHeight() {
        // 确保在浏览器环境中执行
        if (typeof document === 'undefined' || typeof window === 'undefined') return;
  
        // 检测是否是移动设备
        this.checkMobile()
        let bannerHeight = 0
        if (!this.isMobile) {
          // 调整header内容位置
          const headerContent = document.querySelector('.header .header-container');
          // 非移动设备只有两个值，112px和60px，根据showBanner的值来判断
          if (this.showBanner) {
            bannerHeight = 52
            document.documentElement.style.setProperty('--header-height', '112px');
          } else {
            bannerHeight = 0
            document.documentElement.style.setProperty('--header-height', '60px');
          }
          if (headerContent) {
            headerContent.style.marginTop = `${bannerHeight}px`;
            headerContent.style.top = '0';
          }
          return
        }
  
        setTimeout(() => {
          const bannerElement = document.querySelector('.extension-banner');
          if (bannerElement) {
            const bannerHeight = bannerElement.offsetHeight;
            const headerHeight = bannerHeight + 60; // banner高度 + 基础header高度
            // 更新CSS变量
            document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
  
            // 调整header内容位置
            const headerContent = document.querySelector('.header .header-container');
            if (headerContent) {
              headerContent.style.marginTop = `${bannerHeight}px`;
              headerContent.style.top = '0';
            }
          }
        }, 100); // 延迟确保DOM已更新
      },
      
      /**
       * 更新横幅可见性
       */
      updateBannerVisibility(visible) {
        // 确保只在浏览器环境中执行
        if (typeof document === 'undefined' || window.innerWidth <= 768) return;
  
        this.showBanner = visible

        this.$store.dispatch('cgAuth/setShowBanner', visible)
  
        // 检测是否是移动设备
        this.checkMobile()
  
        // 更新header高度CSS变量
        if (this.isMobile && visible) {
          // 移动端下，动态计算banner高度
          this.calculateMobileBannerHeight();
        } else {
          // 非移动端使用固定高度值
          document.documentElement.style.setProperty('--header-height', `${visible ? 112 : 60}px`);
          this.calculateMobileBannerHeight();
        }
      },
      
      /**
       * 跳转到菜单
       */
      navigateToMenu(command) {
        switch (command) {
          case 'learn-more':
            window.open('https://www.learning-genie.com/1-ai-supplementary-curriculum/', '_blank')
            break;
          case 'install-plugin':
            window.open('https://curriculumgenie.learning-genie.com', '_blank')
            break;
          case 'user-profile':
            this.openUserProfile();
            break;
          default:
            break;
        }
      },
      
      /**
       * 处理点击菜单事件
       * @param key 菜单索引
       */
      handleSelectMenu(index) {
        if (index === 'learn-more') {
          window.open('https://www.learning-genie.com/1-ai-supplementary-curriculum/', '_blank')
        } else if (index === 'install-plugin') {
          window.open('https://curriculumgenie.learning-genie.com', '_blank')
        } else {
          this.$router.push({
            path: index
          })
        }
      },
  
      /**
       * 打开登录弹窗
       */
      openLoginDialog() {
        // 增加埋点
        this.$analytics.sendEvent('cg_topnav_login')
        const loginDialog = this.$refs.loginDialog
        loginDialog && loginDialog.open()
      },
  
      /**
       * 关闭登录弹窗
       */
      closeLoginDialog() {
        const loginDialog = this.$refs.loginDialog
        if (loginDialog) {
          loginDialog.close()
        }
      },
  
      /**
       * 打开用户资料页面
       */
      openUserProfile() {
        // 增加埋点
        this.$analytics.sendEvent('cg_topnav_avatar')
        // 跳转到用户资料页面
        window.location.href = '/user-profile'
      },
  
      /**
       * 创建邀请记录
       */
      async createInvitationRecord() {
        // 实现创建邀请记录的逻辑
        try {
          const invitationApi = useInvitationApi()
  
          // 先获取邀请链接
          const inviteLinkResponse = await invitationApi.getInvitationLink()
          let invitationCode = ''
  
          // 从邀请链接中提取邀请码
          if (inviteLinkResponse && inviteLinkResponse.invite_link) {
            const url = new URL(inviteLinkResponse.invite_link)
            invitationCode = url.searchParams.get('c') || ''
          }
  
          if (!invitationCode) {
            return
          }
  
          // 准备创建邀请记录的参数
          const params = {
            invitation_code: invitationCode,
            invited_user_id: (this.currentUser && this.currentUser.user_id) || undefined,
            invitation_email: (this.currentUser && this.currentUser.email) || undefined,
            type: 'PLUGIN_INSTALL' // 标识为插件安装类型
          }
  
          // 调用API创建邀请记录
          const result = await invitationApi.createInvitationRecord(params)
  
          // 将 localStorage 中的banner显示次数修改为最大值，后续不再弹出弹窗
          try {
            // 获取当前banner状态
            const bannerState = localStorage.getItem('cg_banner_state')
            if (bannerState) {
              const state = JSON.parse(bannerState)
              // 设置为最大显示次数，确保不再显示
              state.showCount = 3 // 假设最大显示次数为3
              // 标记为手动关闭，避免再次显示
              state.closedManually = true
              localStorage.setItem('cg_banner_state', JSON.stringify(state))
            }
            // 设置横幅可见性标志为false
            localStorage.setItem('cg_banner_visibility', 'false')
  
            // 记录插件安装状态
            localStorage.setItem('cg_plugin_installed', 'true')
  
            // 设置插件安装状态
            this.$store.dispatch('cgAuth/setHasPlugin', true)
          } catch (e) {
          }
        } catch (error) {
        }
      },
  
      /**
       * 处理移动端菜单选择
       */
      handleMobileMenuSelect(index) {
        this.showMobileMenu = false
        if (index === 'learn-more') {
          window.open('https://www.learning-genie.com/1-ai-supplementary-curriculum/', '_blank')
        } else if (index === 'install-plugin') {
          window.open('https://curriculumgenie.learning-genie.com', '_blank')
        } else if (index === '/user-profile' || index === '/') {
          window.location.href = index
        } else {
          this.$router.push({
            path: index
          })
        }
      },

      /**
       * 处理 Logo 点击事件
       */
      handleLogoClick () {
        if (this.currentUser) {
          // 已登录，打开 Unit Planner
          // 判断当前界面是否是 Unit Planner，如果不是，则打开 Unit Planner
          if (this.$route.path !== '/curriculum-genie/unit-planner') {
            this.$router.push({
              path: '/curriculum-genie/unit-planner'
            })
          }
        }
      }
    }
  }
</script>
  
<style lang="scss" scoped>
  .header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: white;
    box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.10);
    height: 60px; /* 基础高度 */
  
    &.has-banner {
      height: 112px; /* 带横幅时的高度 */
    }
  }
  
  .header-container {
    max-width: 1240px;
    height: 60px;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    height: 100%;
  }
  
  .header-middle {
    flex: 1;
  }
  
  .header-right {
    min-width: auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  
  .logo-image {
    height: 100%;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin-right: 0.5rem;
    width: auto;
    object-fit: contain;
  }
  
  .logo-image-large {
    max-width: 160px;
  }
  
  @media (min-width: 768px) {
    .logo-image-large {
      max-width: 200px;
    }
  }
  
  .header-menu {
    flex: 1;
    border-bottom: none;
    overflow-x: auto;
    white-space: nowrap;
  }
  
  .unit-planner-btn {
    margin-right: 1rem;
  }
  
  .chrome-btn, .login-btn {
    margin-left: 1rem;
  }
  
  .mobile-menu-btn {
    margin-left: 1rem;
  }
  
  .mobile-menu {
    padding: 1rem;
  }
  
  .user-info {
    margin-bottom: 1.5rem;
  }
  
  .user-avatar {
    margin-bottom: 0.5rem;
  }
  
  .user-email {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .menu-items {
    display: flex;
    flex-direction: column;
  }
  
  .menu-item {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
  }
  
  .menu-item:hover {
    background-color: #f3f4f6;
  }
  
  .download-container {
    margin-top: 1.5rem;
  }
  
  .download-btn {
    width: 100%;
  }
  
  // 添加横幅样式覆盖，确保正确显示在header内容之上
  /deep/ .extension-banner {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1001;
  }
  
  // 当横幅显示时，为header内容添加上边距
  .header-container {
    position: relative;
    top: 0;
    z-index: 1000;
    height: 60px;
  
    .has-banner & {
      top: 52px;
    }
  }
  
  // 添加菜单占位样式
  /deep/ .el-menu {
    border-right: none;
    height: 60px;
    background-color: #fff;
    box-sizing: border-box;
    margin: 0;
    padding-left: 0;
    display: flex;
    flex-wrap: nowrap;
    min-width: auto;
  
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  /deep/ .el-menu-item {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    margin: 0;
    padding: 0 20px;
    list-style: none;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 1rem;
    color: #303133;
  }
  
  /deep/ .el-menu-item.is-active {
    font-weight: 600;
    color: #111C1C !important;
  }
  
  /* 移动端菜单样式 */
  .mobile-menu {
    .menu-items > div {
      position: relative;
    }
  
    .menu-items > div.active {
      color: var(--el-color-primary);
      font-weight: 600;
    }
  }
  
  /* 添加响应式样式 */
  @media (max-width: 768px) {
    .header {
      &.has-banner {
        height: auto; /* 允许自动高度以适应横幅内容 */
        min-height: 112px; /* 设置最小高度 */
      }
    }
  
    // 移动端横幅显示时调整header内容位置
    .header-container {
      .has-banner & {
        top: auto; /* 清除固定顶部距离 */
        margin-top: 84px; /* 为banner预留空间 */
      }
    }
  
    /deep/ .extension-banner {
      position: absolute !important;
      height: auto; /* 允许自动高度 */
    }
  }
  
  // 针对超小屏幕的调整
  @media (max-width: 375px) {
    .header-container {
      .has-banner & {
        margin-top: 90px; /* 更小屏幕给横幅更多空间 */
      }
    }
  }
  </style>
  