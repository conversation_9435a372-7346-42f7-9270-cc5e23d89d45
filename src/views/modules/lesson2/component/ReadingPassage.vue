<template>
  <div class="reading-passage">
    <!-- 使用 LgTabs 作为最外层容器 -->
    <LgTabs v-if="tabs.length > 1" :tabs="tabs" :value="activeTab" @change="handleTabChange" size="mini"
      class="add-margin-b-16" />
    <div class="reading-passage-content" v-for="item in value" :key="item.id" v-show="activeTab === item.level">
      <!-- 标签页内容区域 -->
      <template v-if="activeTab === item.level">
        <div class="anchor-text-controls">
          <div class="control-col w-full">
            <div class="control-label el-form-item__label">
              <span>Anchor Title</span>
            </div>
            <div class="w-full">
              <el-input v-model="item.title" placeholder="请输入 Anchor Title" @input="handleContentChange">
              </el-input>
            </div>
          </div>

          <div class="control-col">
            <div class="control-label el-form-item__label">
              <span>Type of Reading</span>
            </div>
            <div class="">
              <!-- 如果 isLinkAnchorText 为真则禁用按钮 -->
              <el-button plain style="width: 200px;" @click="showReadingTypeDialog = true" :disabled="isLinkAnchorText">
                {{ readingTypeDisplay }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
            </div>
          </div>

          <div class="control-col">
            <div class="control-label el-form-item__label">
              <span>Customize</span>
            </div>
            <div class="">
              <el-dropdown trigger="click">
                <!-- 如果 isLinkAnchorText 为真则禁用 Text Length 按钮 -->
                <el-button style="width: 200px;" plain :disabled="isLinkAnchorText">
                  Text Length
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click="handleTextLengthChange('shorter')">
                    <i class="el-icon-arrow-up"></i>
                    Make Shorter
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleTextLengthChange('longer')">
                    <i class="el-icon-arrow-down"></i>
                    Make Longer
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div class="anchor-text-section">
          <div class="section-header">
            <span class="section-title el-form-item__label">{{ AnchorTextFormTitle }}:</span>
          </div>
          <!-- 展示 anchorLink 信息，包含图片、标题和链接，使用 Element UI 组件 -->
          <div class="anchor-text-content" v-if="isLinkAnchorText">
            <div class="anchor-link">
              <!-- 图片区域 -->
              <el-image class="anchor-link-image" v-if="item.anchorLink.imageUrl" :src="item.anchorLink.imageUrl"
                :alt="item.anchorLink.title" fit="cover" :preview-src-list="[item.anchorLink.imageUrl]">
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <!-- 内容区域 -->
              <div class="anchor-link-content">
                <!-- 标题 -->
                <div class="anchor-link-content-title">
                  {{ item.anchorLink.title }}
                </div>
                <!-- 链接 -->
                <a class="anchor-link-content-url" :href="item.anchorLink.pageUrl" target="_blank"
                  rel="noopener noreferrer">
                  {{ item.anchorLink.pageUrl }}
                </a>
              </div>
            </div>
          </div>

          <div class="anchor-text-content" v-if="!isLinkAnchorText">
            <!-- 富文本编辑器区域 -->
            <div>
              <el-form-item ref="anchorTextContent" prop="anchorTextContent">
                <el-skeleton :rows="4" animated :loading="loading && !item.content || anchorTextLoading">
                  <template>
                    <editor ref="anchorTextEditor" v-model="item.content" :hiddeImpStepSource="!showAnchorTextResources"
                      :showResources="showAnchorTextResources" placeholder="请输入 Anchor Text 内容..."
                      @input="handleContentChange" />
                  </template>
                </el-skeleton>
              </el-form-item>
            </div>
            <!-- Anchor Text 资源内容 -->
            <AnchorTextResources v-show="showAnchorTextResources" :sourcePreview="false" :lesson="lesson"
              :anchorTextData="item" parentComponentName="ReadingPassage" @upAnchorTextData="upAnchorTextData"
              @before-save-resources="anchorTextLoading = true" @save-resources-success="saveResourcesSuccess"
              @save-resources-failed="anchorTextLoading = false" @updateShowResources="updateShowResources"
              ref="anchorTextResources" />
          </div>
        </div>

        <!-- 反思性问题 -->
        <div class="anchor-text-section">
          <div class="section-header">
            <span class="section-title el-form-item__label">Reflection Questions:</span>
          </div>

          <el-form-item ref="anchorTextContent" prop="anchorTextContent">
            <el-skeleton :rows="4" animated :loading="loading && !item.reflectionQuestions">
              <template>
                <editor ref="anchorTextEditor" v-model="item.reflectionQuestions" placeholder="请输入 Anchor Text 内容..."
                  @input="handleReflectionQuestionsChange" />
              </template>
            </el-skeleton>
          </el-form-item>
        </div>

        <div class="anchor-text-section">
          <div class="section-header">
            <span class="section-title el-form-item__label">Teaching Tips for Reading Passage</span>
          </div>
          <!-- Teaching Tips 表格 -->
          <div class="reading-passage-teaching-tips">
            <!--负责切换的 tab -->
            <div class="display-flex justify-content align-items flex-element-center w-full">
              <table class="learners-table domain-table">
                <thead style="background: #DDF2F3;">
                  <tr class="font-size-16 table-row">
                    <!--th 扩展为两列-->
                    <th colspan="2" style="text-align: left; padding-left: 12px; font-weight: 600;"
                      v-html="'forLearnersWithIEPS'"></th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="table-row" v-for="(teachingTip, index) in item.teachingTips" :key="index">
                    <td style="background: #FFF;width: 20%;">
                      <div class="activity-description" v-html="teachingTip.title"></div>
                    </td>
                    <td class="w-full">
                      <div class="content-description">
                        <el-input v-model="teachingTip.content" class="textarea-hover-padding"
                          placeholder="Describe how the activity can be adapted to meet the needs of IEP children here."
                          :autosize="{ minRows: 3 }" resize="none" type="textarea" :maxlength="10000" />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 阅读类型选择弹窗 -->
    <el-dialog title="Type of Reading" :visible.sync="showReadingTypeDialog" :close-on-click-modal="false"
      custom-class="common-dialog" width="480px">
      <div class="reading-type-options">
        <span class="add-margin-b-16">Please select the Type of Reading you want to adjust.</span>
        <div v-for="option in readingTypeOptions" :key="option.value" @click="handleReadingTypeOptionClick(option)"
          class="reading-type-option" :class="{ 'is-checked': readingTypeInDialog === option.value }">
          <!-- 只展示 radio，不显示文字 -->
          <el-radio :label="option.value" v-model="readingTypeInDialog" style="margin: 0;" />
          <div class="reading-type-radio">
            <div class="option-content">
              <div class="option-title">{{ option.label }}</div>
              <div class="option-description">{{ option.description }}</div>
            </div>
          </div>
        </div>
        <el-checkbox class="add-margin-t-24" v-model="isRegenerateReflectionQuestionsAndTeachingTips">Regenerate
          Reflection Questions and Teaching Tips</el-checkbox>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showReadingTypeDialog = false">Cancel</el-button>
        <el-button type="primary" @click="confirmReadingType">Confirm & Generate</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import LgTabs from '@/components/LgTabs'
import AnchorTextResources from '@/views/modules/lesson2/component/anchorTextResources.vue'
import editor from '@/views/modules/lesson2/component/editor/index.vue'

export default {
  name: 'ReadingPassage',
  components: {
    LgTabs,
    AnchorTextResources,
    editor
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    lesson: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    isCenterLesson: {
      type: Boolean,
      default: false
    },
    showAnchorTextResources: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: null,
      anchorTextLoading: false, // Anchor Text 加载状态
      showReadingTypeDialog: false, // 控制阅读类型选择弹窗显示
      readingType: null,
      isRegenerateReflectionQuestionsAndTeachingTips: false, // 是否重新生成反思问题和教学提示
      readingTypeInDialog: null, // 阅读类型选择弹窗中选中的阅读类型
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.activeTab = newVal[0].level
        }
      },
      immediate: true,
      deep: true
    },
    showAnchorTextResources: {
      handler(newVal) {
        if (this.$refs.anchorTextResources) {
          this.$refs.anchorTextResources.showResources = newVal
        }
      },
      immediate: true
    }
  },
  computed: {
    tabs () {
      return this.value.map(item => ({
        label: item.level,
        name: item.level
      }))
    },
    readingTypeDisplay () {
      if (this.activeAnchorTextData.readingType || this.activeAnchorTextData.readingType === 'NONE') {
        if (this.activeAnchorTextData.type === 'ANCHOR_TEXT') {
          return 'Anchor Text'
        } else if (this.activeAnchorTextData.type === 'INFORMATIONAL_TEXT') {
          return 'Informational Text'
        }
      }
      switch (this.activeAnchorTextData.readingType) {
        case 'POEM':
          return 'Poem'
        case 'EXCERPT':
          return 'Excerpt'        
        case 'SHORT_STORY':
          return 'Short Story'
        default:
          return 'None'
      }
    },
    activeAnchorTextData () {
      return this.value.find(item => item.level === this.activeTab)
    },
    isLinkAnchorText () {
      return this.activeAnchorTextData.anchorLink && !!this.activeAnchorTextData.anchorLink.pageUrl
    },
    readingTypeOptions () {
      // 基础选项数组
      const options = [
        { label: 'Poem', value: 'POEM', description: 'Transform into a creative, rhythmic form' },
        { label: 'Excerpt', value: 'EXCERPT', description: 'Extract meaningful sections or passages from the text' },
        { label: 'Short Story', value: 'SHORT_STORY', description: 'Transform into a creative, rhythmic form' }
      ]
      // 根据 activeAnchorTextData 类型，将对应选项插入到第一个位置
      if (this.activeAnchorTextData) {
        if (this.activeAnchorTextData.type === 'ANCHOR_TEXT') {
          // ANCHOR_TEXT 类型，将 Anchor Text 选项插入第一个
          options.unshift({ label: 'Anchor Text', value: 'ANCHOR_TEXT', description: 'Deliver factual and explanatory information' })
        }
        if (this.activeAnchorTextData.type === 'SUPPORTING_TEXT') {
          // SUPPORTING_TEXT 类型，将 Informational Text 选项插入第一个
          options.unshift({ label: 'Informational Text', value: 'NONE', description: 'Deliver factual and explanatory information ' })
        }
      }
      return options
    },
    AnchorTextFormTitle () {
      if (this.activeAnchorTextData.type === 'ANCHOR_TEXT') {
        return 'Anchor Text'
      }
      return 'Informational Text'
    }
  },
  methods: {
    /**
     * 处理文本长度变化
     * @param length 文本长度
     */
    handleTextLengthChange (length) {
      console.log(length)
    },
    handleTabChange(tab) {
      this.activeTab = tab.name
    },

    // 切换显示/隐藏 Anchor Text 资源
    changeShowAnchorTextSource() {
      this.showAnchorTextResources = !this.showAnchorTextResources
      // 如果当前是要隐藏资源，则同时关闭引导弹窗
      if (!this.showAnchorTextResources) {
        // 如果 anchorTextResources 组件引用存在，直接设置 showGuidePopup为false
        if (this.$refs.anchorTextResources) {
          this.$refs.anchorTextResources.showGuidePopup = false
        }
      }
    },

    // 处理 Anchor Text 数据更新
    upAnchorTextData(data) {
      debugger
      let activeAnchorTextData = this.value.find(item => item.level === this.activeTab)
      this.$set(activeAnchorTextData, 'content', data.content)
      this.$set(activeAnchorTextData, 'resources', data.resources)
      this.$set(activeAnchorTextData, 'moreResources', data.moreResources)
      // 更新 Anchor Text 相关数据
      this.$emit('upAnchorTextData', data)
    },

    // 资源保存成功回调
    saveResourcesSuccess() {
      this.anchorTextLoading = false
      this.$emit('save-resources-success')
    },

    // 输入内容变化处理
    handleContentChange() {
      this.$emit('input', [this.activeAnchorTextData])
    },

    handleReflectionQuestionsChange() {
      this.$emit('input', [this.activeAnchorTextData])
    },

    handleReadingTypeOptionClick(option) {
      this.readingTypeInDialog = option.value
    },

    // 确认阅读类型选择
    confirmReadingType() {
      this.showReadingTypeDialog = false
      console.log("确认阅读类型选择")
    },
    updateShowResources (showResources) {
      this.$emit('updateShowResources', showResources)
    }
  }
}
</script>

<style lang="scss" scoped>
.reading-passage-content {
  display: flex;
  gap: 24px;
  flex-direction: column;
}
.reading-passage {
  .anchor-link {
    display: flex;
    padding: 12px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    background: #F5F6F8;
    .anchor-link-image {
      width: 60px;
      height: 50px;
      border-radius: 4px;
    }
    .anchor-link-content {
      display: flex;
      flex-direction: column;
      .anchor-link-content-title {
        font-size: 16px;
      }
      .anchor-link-content-url {
        font-size: 12px;
        color: #10B3B7;
      }
    }
  }
  .anchor-text-controls {
    display: flex;
    gap: 12px;
    justify-content: flex-start;

    .control-col {
      display: flex;
      flex-direction: column;
    }

    /deep/ .el-button.is-plain { 
      span {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    /deep/ .el-button.is-disabled.is-plain {
      background-color: #F5F6F8!important;
      color: #111C1C!important;
      border: 1px solid #DCDFE6!important;
    }
  }

  .anchor-text-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .resource-controls {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .anchor-text-content {
      gap: 10px;
    }
  }

}

.reading-type-options {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .reading-type-option {
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    display: flex;
    padding: 12px;
    flex-direction: row;
    gap: 8px;
    cursor: pointer;

    /deep/ .el-radio__label {
      display: none;
    }

    /deep/ .el-radio__input {
      margin-top: 4px;
    }

    .reading-type-radio {

      .option-title {
        color: #111C1C;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }

      .option-description {
        color: #676879;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }

  .reading-type-option.is-checked {
    border: 2px solid #10B3B7;
    background: #DDF2F3;
  }
}
</style>


<style lang="less" scoped>
.reading-passage-teaching-tips {
    width: 100%;
    
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      align-self: center;
      border-radius: 4px;
      width: 100%;
    
      .learners-table {
        table {
          border-collapse: collapse;
          width: 100%;
        }
    
        .activity-description {
          display: flex;
          padding: 12px;
          color: var(--color-text-secondary);
          font-feature-settings: 'clig' off, 'liga' off;
    
          font-size: 14px;
          font-weight: 600;
          line-height: 24px;
          /* 150% */
        }
    
        .content-description {
          display: flex;
          width: 100%;
          justify-content: space-between;
          align-items: flex-start;
          flex: 1 0 0;
          color: var(--color-text-primary);
          font-feature-settings: 'clig' off, 'liga' off;
    
          /* Regular/16px */
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          /* 150% */
        }
      }
    /deep/ .el-select {
      margin: 10px;
    }

  /deep/ .el-input__inner {
      border-style: dashed;
  }

  /deep/ .el-textarea__inner {
      border-color: transparent;
      resize: none;
      overflow: hidden;
  }

  /deep/ .el-textarea__inner:hover {
      border: 1px dashed #c0c4cc;
  }

  /deep/ .el-textarea__inner:focus {
      border: 1px dashed #10b3b7;
  }

  .textarea-hover-padding-skeleton {
      & > :first-child {
          padding: 10px 15px;

          [contenteditable="true"] {
              outline: none;
          }

          // iep support 使用 editor 组件，这里对其进行样式修改
          .teacherChildrenContent /deep/ .quill-editor {
              line-height: normal;
              height: 100%;

              h1, h2, h3, h4, h5, h6 {
                  line-height: 1.42 !important;
              }

              // 隐藏功能栏
              .ql-toolbar {
                  display: none !important;
              }

              .ql-container {
                  font-size: 14px !important;
                  border: 1px dashed transparent !important;
                  resize: none !important;
                  overflow: hidden !important;
                  border-radius: 4px !important;
              }

              .ql-editor {
                  border: 1px dashed transparent !important;
              }

              .ql-editor:hover {
                  border: 1px dashed var(--color-inner-dashed-border) !important;
              }

              .ql-editor:focus {
                  border: 1px dashed var(--color-primary) !important;
              }

              .ql-editor {
                  overflow-y: auto;
                  min-height: 33px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
                  &.ql-blank:before {
                      font-style: normal;
                  }
              }

              /* 修改富文本编辑器的placeholder的默认颜色：字体 */

              .ql-editor[data-placeholder]:before {
                  /* 此处设置 placeholder 样式 */
                  font-style: normal;
                  font-size: 14px;
              }
          }
      }
  }

  .textarea-hover-padding {
      padding: 10px 15px;
  }

  .textarea-hover-padding:hover, .textarea-hover-padding:focus-within {
      padding: 10px 15px;
  }
  .domain-table {
    border-radius: 4px;
    border: 1px solid #10B3B7;
    width: 100%;
    border-collapse: collapse;
    color: #323338;

    tr,
    th,
    td {
        border: 1px solid #10B3B7;
    }

    th {
        height: 40px;
    }
  }


  .table-row {
      position: relative;
  }

}

.is-error {
    .domain-table {
        border: 1px solid red;

        tr,
        th,
        td {
            border: 1px solid red;
        }
    }
}
</style>