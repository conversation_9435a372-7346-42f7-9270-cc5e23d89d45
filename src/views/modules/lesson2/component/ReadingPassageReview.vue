<template>
  <el-card shadow="never" class="w-full reading-passage-view-card add-margin-t-30"
    body-style="height: 100%;">
    <!-- 卡片头部 -->
    <div slot="header" class="portrait-header display-flex justify-content-center align-items">
      <div class="title-container">
        <div class="left-gradient title-gradient"></div>
        <div class="title">
          {{ readingPassageTitle }}
        </div>
        <div class="right-gradient title-gradient"></div>
      </div>
    </div>
    <div class="reading-passage-view-content lg-padding-24">
      <!-- readingPassage 内容 -->
      <div class="reading-passage">
        <!-- 使用 LgTabs 作为最外层容器 -->
        <LgTabs v-if="tabs.length > 1" :tabs="tabs" :value="activeTab" @change="handleTabChange" size="mini"/>
        <div class="reading-passage-content" v-for="(item, index) in lesson.readingPassage" :key="index" v-show="activeTab === item.level">
          <!-- 标签页内容区域 -->
          <template v-if="activeTab === item.level">
            <div class="anchor-text-section">
              <div class="lesson-field-value" style="font-size: 18px;font-weight: 600;">{{ item.title }}</div>
            </div>
            <div class="anchor-text-section">
              <div class="section-header">
                <span class="section-title">{{ AnchorTextFormTitle }}:</span>
              </div>
              <!-- 展示 anchorLink 信息，包含图片、标题和链接，使用 Element UI 组件 -->
              <div class="anchor-text-content" v-if="isLinkAnchorText">
                <div class="anchor-link">
                  <!-- 图片区域 -->
                  <el-image
                    class="anchor-link-image"
                    v-if="item.anchorLink.imageUrl"
                    :src="item.anchorLink.imageUrl"
                    :alt="item.anchorLink.title"
                    fit="cover"
                    :preview-src-list="[item.anchorLink.imageUrl]"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                  <!-- 内容区域 -->
                  <div class="anchor-link-content">
                    <!-- 标题 -->
                    <div class="anchor-link-content-title">
                      {{ item.anchorLink.title }}
                    </div>
                    <!-- 链接 -->
                    <a
                      class="anchor-link-content-url"
                      :href="item.anchorLink.pageUrl"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {{ item.anchorLink.pageUrl }}
                    </a>
                  </div>
                </div>
              </div>

              <div class="anchor-text-content" v-if="!isLinkAnchorText">
                <!-- 富文本内容展示区域 -->
                <div>
                  <div class="lesson-field-value ql-editor" v-html="item.content"></div>
                </div>
                <!-- Anchor Text 资源内容 -->
                <AnchorTextResources
                  :resourcesPreview="true"
                  :lesson="lesson"
                  :anchorTextData="item"
                  :isPreview="true"
                  parentComponentName="ReadingPassageReview"
                  :updateShowResources="updateShowResources"
                  ref="anchorTextResources"
                  />
              </div>
            </div>

            <!-- 反思性问题 -->
            <div class="anchor-text-section">
              <div class="section-header">
                <span class="section-title ">Reflection Questions:</span>
              </div>
              <div class="lesson-field-value ql-editor" v-html="item.reflectionQuestions"></div>
            </div>

            <div class="anchor-text-section">
              <!-- Teaching Tips 表格 -->
              <div class="reading-passage-teaching-tips">
                <table class="learners-table domain-table">
                  <thead style="background: #DDF2F3;">
                    <tr class="font-size-16 table-row">
                        <!--th 扩展为两列-->
                        <th colspan="2" style="text-align: left; padding-left: 12px; font-weight: 600;">Teaching Tips for Reading Passage</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr class="table-row" v-for="(teachingTip, index) in item.teachingTips" :key="index">
                        <td style="background: #FFF;width: 20%;">
                            <div class="activity-description" v-html="teachingTip.title"></div>
                        </td>
                        <td class="w-full">
                            <div class="content-description">
                              {{ teachingTip.content  }}
                            </div>
                        </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import LgTabs from '@/components/LgTabs'
import AnchorTextResources from '@/views/modules/lesson2/component/anchorTextResources.vue'
import editor from '@/views/modules/lesson2/component/editor/index.vue'
import ReadingPassage from '@/views/modules/lesson2/component/ReadingPassage.vue'
export default {
  name: 'ReadingPassageReview',
  components: {
    LgTabs,
    AnchorTextResources,
    editor,
    ReadingPassage
  },
  props: {
    lesson: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeTab: null
    }
  },
  watch: {
    lesson: {
      handler(newVal) {
        if (newVal && newVal.readingPassage && newVal.readingPassage.length > 0) {
          this.activeTab = newVal.readingPassage[0].level
        }
      },
      immediate: true,
      deep: true
    },
  },
  computed: {
    /**
     * 计算标题
     */
     readingPassageTitle () {
      let title = 'Reading Passage'
      // 判断 lesson.readingPassage 是否为数组且长度为 1
      if (this.lesson && Array.isArray(this.lesson.readingPassage) && this.lesson.readingPassage.length === 1) {
        const passage = this.lesson.readingPassage[0]
        // 如果有 level 字段，拼接 level
        if (passage.level) {
          title += ' - ' + passage.level
        }
      }
      return title
    },
    AnchorTextFormTitle () {
      if (this.activeAnchorTextData.type === 'ANCHOR_TEXT') {
        return 'Anchor Text'
      }
      return 'Informational Text'
    },
    tabs () {
      if (!this.lesson || !this.lesson.readingPassage) {
        return []
      }
      return this.lesson.readingPassage.map(item => ({
        label: item.level,
        name: item.level
      }))
    },
    readingTypeDisplay () {
      if (!this.activeAnchorTextData) {
        return 'None'
      }
      if (this.activeAnchorTextData.readingType || this.activeAnchorTextData.readingType === 'NONE') {
        if (this.activeAnchorTextData.type === 'ANCHOR_TEXT') {
          return 'Anchor Text'
        } else if (this.activeAnchorTextData.type === 'INFORMATIONAL_TEXT') {
          return 'Informational Text'
        }
      }
      switch (this.activeAnchorTextData.readingType) {
        case 'POEM':
          return 'Poem'
        case 'EXCERPT':
          return 'Excerpt'
        case 'SHORT_STORY':
          return 'Short Story'
        default:
          return 'None'
      }
    },
    activeAnchorTextData () {
      if (!this.lesson || !this.lesson.readingPassage) {
        return null
      }
      return this.lesson.readingPassage.find(item => item.level === this.activeTab)
    },
    isLinkAnchorText () {
      if (!this.activeAnchorTextData) {
        return false
      }
      return this.activeAnchorTextData.anchorLink && !!this.activeAnchorTextData.anchorLink.pageUrl
    },
  },
  methods: {
    handleTabChange(tab) {
      this.activeTab = tab.name
    },
    updateShowResources(showResources) {
      this.$emit('updateShowResources', showResources)
    }
  }
}
</script>

<style lang="scss" scoped>
.reading-passage-view-card {
  border-radius: 20px;
  overflow: hidden;
  background: #DDF2F3;
  border: 1px solid #10B3B7;
}

.portrait-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 16px 24px;
  align-self: center;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;

  .title-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .title {
    color: #10B3B7;
    font-feature-settings: 'liga' off, 'clig' off;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
  }
}
/deep/ .el-card__header {
  padding: 0 !important;
  border-bottom: none;
}

/deep/ .el-card__body {
  padding: 0;
  border-radius: 16px 16px 0 0;
  background: #FFF;
}
/* 描述列表标签值 */
.lesson-field-value {
  font-size: 16px;
  line-height: 25px;
  color: #111c1c;
}
.lesson-field-value.ql-editor {
  padding: 0;
}

.reading-passage-content {
  display: flex;
  gap: 24px;
  flex-direction: column;
}
.reading-passage {
  display: flex;
  gap: 24px;
  flex-direction: column;

  .anchor-link {
    display: flex;
    padding: 12px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    background: #F5F6F8;
    .anchor-link-image {
      width: 60px;
      height: 50px;
      border-radius: 4px;
    }
    .anchor-link-content {
      display: flex;
      flex-direction: column;
      .anchor-link-content-title {
        font-size: 16px;
      }
      .anchor-link-content-url {
        font-size: 12px;
        color: #10B3B7;
      }
    }
  }
  .anchor-text-controls {
    display: flex;
    gap: 12px;
    justify-content: flex-start;

    .control-col {
      display: flex;
      flex-direction: column;
    }
  }

  .anchor-text-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .resource-controls {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .anchor-text-content {
      gap: 10px;
    }
  }
  .section-title {
    color: #111C1C;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
}
.reading-passage-view-content {
  .anchor-text-source-content-preview {
    border: none;
    padding: 16px 0 0 0;
  }
}
</style>

<style lang="less" scoped>
.reading-passage-teaching-tips {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: center;
  border-radius: 4px;
  width: 100%;

  .learners-table {
      table {
        border-collapse: collapse;
        width: 100%;
      }
  
      .activity-description {
        display: flex;
        padding: 12px;
        color: var(--color-text-secondary);
        font-size: 14px;
        font-weight: 600;
        line-height: 24px;
        /* 150% */
      }
  
      .content-description {
        padding-left: 12px;
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: flex-start;
        flex: 1 0 0;
        color: var(--color-text-primary);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 150% */
      }
    }

  .domain-table {
    border-radius: 4px;
    border: 1px solid #10B3B7;
    width: 100%;
    border-collapse: collapse;
    color: #323338;

    tr,
    th,
    td {
        border: 1px solid #10B3B7;
    }

    th {
        height: 40px;
    }
  }

  .table-row {
      position: relative;
  }
}
</style>