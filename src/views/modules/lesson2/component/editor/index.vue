<template>
  <div class="notranslate">
    <quill-editor v-model="content" :options="editorOption" ref="editor" @change="handleChange($event)"
    v-show="showQuillEditor"
                  @blur="$emit('blur')"
                  :class="['editor',{validate:_validateEvent},{ 'editor-hide-imp-step-subscript': hiddeImpStepSource }, { 'editor-show-resources': showResources }]"/>
  </div>
</template>
<script>
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { quillEditor } from 'vue-quill-editor'
import Quill from 'quill'
import emitter from 'element-ui/src/mixins/emitter'
import ImpScriptBlot from '@/utils/impScriptBlot'

const Size = Quill.import('attributors/style/size')
Size.whitelist = ['14px','16px', '18px', '20px', '22px']
Quill.register(Size, true)

// 自定义字体类型
const Font = Quill.import('attributors/style/font')
Font.whitelist = ['Sans-Serif', 'Serif', 'Monospace']
Quill.register(Font, true)

// 自定义标签
Quill.register(ImpScriptBlot, true) // 注册自定义标签

export default {
  name: 'Editor',
  components: { quillEditor },
  props: {
    value: {
      default: null
    },
    placeholder: {
      default: null
    },
    maxlength: {
      default: null
    },
    validateEvent: {
      default: null
    },
    hiddeImpStepSource: {
      type: Boolean,
      default: false // 默认为 false
    }, // 是否显示实施步骤资源
    showResources: { // 是否显示实施步骤资源，默认为 true
      type: Boolean,
      default: false
    },
    impStepModel: {
      default: false
    } // 是否是实施步骤模块启动的编辑器
  },
  mixins: [emitter],
  computed: {
    _maxLength () {
      return this.maxLength || 10000
    },
    _validateEvent () {
      return this.validateEvent || this.validateEvent === undefined;
    }
  },
  data () {
    return {
      content: this.value || '',
      showQuillEditor: false, // 是否显示 quill 编辑器
      editorOption: {
        modules: {
          toolbar: [
            [{ font: Font.whitelist }],
            [{ size: Size.whitelist }],
            [{ header: [] }],
            [{'list': 'ordered'}, { 'list': 'bullet'}],
            [{'align': []}],
            [{'indent': '-1'}, {'indent': '+1'}],
            ['bold', 'italic', 'underline', 'strike'],
            [{'color': []}, {'background': []}]
          ]
        },
        theme: 'snow',
        placeholder: this.placeholder
      }
    }
  },
  watch: {
    value() {
      this.content = this.value;
    },
    content(value) {
      this.$emit("input", value);
      if (this._validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', [value]);
      }
    }
  },
  mounted() {
    // 通过延迟显示组件的方式，解决自动聚焦的问题
    this.showQuillEditor = false
      setTimeout(() => {
        this.showQuillEditor = true
        this.$nextTick(() => {
          // 获取编辑器实例
          if (this.$refs.editor) {
            // 监听编辑器的文本变化
            this.$refs.editor.quill.on('text-change', () => {
              // 如果内容不为空，确保不显示占位符
              this.handlePlaceholder()
            })
            
            // 监听编辑器获得焦点事件
            this.$refs.editor.quill.root.addEventListener('focus', () => {
              this.handlePlaceholder()
            })
          }
        })
      }, 100)
  },
  methods: {
    // 处理占位符显示逻辑
    handlePlaceholder() {
      const quill = this.$refs.editor.quill
      const editorContent = quill.getText().trim()
      const editorElement = quill.root
      
      if (editorContent === '') {
        // 内容为空时，确保占位符可见
        editorElement.classList.add('ql-blank')
      } else {
        // 内容不为空时，确保占位符不可见
        editorElement.classList.remove('ql-blank')
      }
    },
    
    handleChange (event) {
      let length = event.quill.getLength();
      if (length > this._maxLength) {
        event.quill.deleteText(this._maxLength, length - this._maxLength);
      }

      // 检验内容是否只有空白符
      const text = event.quill.getText().trim();
      if (text === '') {
        // 内容为空或只包含空白符时，清空内容
        this.content = '';
      }

      // 当内容改变时，向 UDL 组件发送数据改变的事件，针对于改编之后的课程的 UDL 的 support 部分的修改
      this.$emit('updateUniversalDesignForLearningGroup')
    }
  }
}
</script>
<style scoped lang="less">
.editor /deep/ & {
  line-height: normal;
  height: 100%;

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.42 !important;
  }

  .ql-toolbar {
    border-bottom: none;
    padding-bottom: 0;
    border-radius: 4px;

    .ql-size.ql-picker {
      width: 60px;
    }
  }

  .ql-container {
    font-size: 14px;
    height: calc(100% - 35px);
    border-radius: 0 0 4px 4px; // 顶部圆角为 0，底部为 4px
  }

  .ql-editor {
    overflow-y: auto;
    min-height: 100px; // 最小高度100px，防止快速添加课程中的编辑框高度太矮
    &.ql-blank:before {
      font-style: normal;
    }
  }
}
.editor-show-resources {
  /deep/.ql-container {
    border-radius: 0;
    border-bottom: 0;
  }
}
.editor-hide-imp-step-subscript /deep/ & {
  //.ql-editor span[style*="color: rgb(16, 179, 183)"] {
  //  display: none;
  //}

  .ql-editor imp-script {
    display: none;
  }
}
.el-form-item.is-error .editor.validate /deep/ & {
  .ql-toolbar, .ql-container {
    border-color: red;
  }
}
</style>
<style lang="less">
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
  content: '12px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  content: '16px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='18px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='18px']::before {
  content: '18px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  content: '20px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='22px']::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='22px']::before {
  content: '22px';
}
/*字体：'Sans-Serif', 'Serif', 'Monospace'*/
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='Sans-Serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='Sans-Serif']::before {
  content: 'Sans-Serif';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='Serif']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='Serif']::before {
  content: 'Serif';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='Monospace']::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='Monospace']::before {
  content: 'Monospace';
}

.ql-editor.ql-blank::before {
  content: attr(data-placeholder);
  color: #999; /* 可以设置颜色 */
  font-size: 14px !important; /* 在这里设置字体大小 */
  font-style: italic; /* 可以设置字体样式 */
  pointer-events: none;
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 5px;
}

/* 恢复这个规则，但现在由JavaScript控制ql-blank类的添加和移除 */
.ql-editor.ql-blank:focus::before {
  content: attr(data-placeholder);
}
</style>