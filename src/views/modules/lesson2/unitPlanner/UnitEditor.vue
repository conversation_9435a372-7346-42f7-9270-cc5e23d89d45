<template>
    <div class="lg-padding-bottom-20 display-flex flex-direction-col h-full" :style="{ 'padding-top': paddingTop }" :class="{'wrapper-md': isCG || isCurriculumPlugin, 'mobile-padding': isMobile}">
        <div class="display-flex justify-content align-items steps-outer" v-show="showSteps" :class="{'mobile-flex-column': isMobile}">
            <!-- 步骤 -->
            <div class="steps-container display-flex align-items-center flex-1" v-if="!lightAdapt && !getUnitLoading">
              <!-- Prompt 按钮和弹窗 -->
              <div class="prompt-btn-container" v-if="step === 0 && showPromptBtn">

                <el-popover
                  v-model="showPromptPanel"
                  placement="bottom-start"
                  :popper-class="isMobile ? 'mobile-popover' : 'desktop-popover'"
                  trigger="manual"
                  :get-popup-container="() => currentRouteComponent.promptPopoverRef">
                  <el-button slot="reference"
                             class="prompt-btn"
                             type="primary"
                             :disabled="promptLoading"
                             plain
                             @click="handlePromptPanel">
                    Prompt <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <!-- 弹窗头部 -->
                  <div class="dialog-header display-flex justify-content-between align-items-center prompt-title">
                    <div class="font-size-16 font-bold ">Prompt</div>
                    <i class="el-icon-close lg-pointer font-size-18 font-bold" @click="showPromptPanel = false"></i>
                  </div>
                  <unit-info-form
                    ref="unitInfoForm"
                    @close="showPromptPanel = false"
                    :showPromptPanel="showPromptPanel"
                    :initHistory="false"
                    :showDesAndVoiceBtn="isAdaptImportUnit"
                    :showHeadBtn="!isAdaptImportUnit"
                    @notifySync="notifySync"
                    class="unit-info-form-scroll"
                  ></unit-info-form>
                  <div class="m-t-sm sticky-bottom" :class="{'mobile-margin': isMobile}">
                    <!-- 生成课程信息  v-if="!unitOverview.overview" -->
                    <el-button type="primary"
                               class="w-full mt-2 generate-style ai-btn"
                               @click="handleRegenerate">
                      <template #icon>
                        <i class="lg-icon lg-icon-generate font-bold"></i>
                      </template>
                      {{$t('loc.unitPlannerStep1Regenerate')}}
                    </el-button>
                  </div>
                </el-popover>
              </div>
              <div v-if="isGenieCurriculumToUnitPlanThird" class="title-font-20">
                {{ $t('loc.unitPlannerStep1LessonPlanIdeas') }}
              </div>
              <el-steps v-else :active="step" align-center class="m-b-sm unit-steps">
                <el-step :title="$t('loc.unitPlannerStep1UnitFoundations')"></el-step>
                <el-step :title="$t('loc.unitPlannerStep1WeeklyTheme')"></el-step>
                <el-step :title="$t('loc.unitPlannerStep1LessonPlanIdeas')"></el-step>
              </el-steps>
            </div>
            <!-- light 特属处理 -->
             <div v-if="lightAdapt" class="light-adapt-header">
                <!-- 返回按钮 -->
                <div class="light-adapt-back">
                  <el-button
                    type="text"
                    icon="lg-icon lg-icon-back font-size-20"
                    @click="handleLightAdaptBack"
                    class="back-button">
                     Lesson Plan Details
                  </el-button>
                </div>
                
                <!-- Tab 切换 -->
                <lg-tabs
                class="w-full text-center"
                style="display: flex; justify-content: center; margin-right: 150px;"
                v-model="activeTab"
                :tabs="lightAdaptTabs"
                @change="handleTabChange"></lg-tabs>
             </div>

            <!-- 管理 Roster -->
            <el-button v-show="showRosterBtn" size="small">
                <a class="lg-pointer" :href="rosterPath" target="_blank">
                    <i class="el-icon-s-tools font-size-18" ></i>
                    Demographics
                </a>
            </el-button>
        </div>
        <!-- 步骤详情页 -->
        <router-view v-show="!getUnitLoading" ref="currentRouteComponent" @promptBtnVisibilityChange="handlePromptBtnVisibilityChange" @promptPanelChange="handlePromptPanelChange" @promptLoadChange="handlePromptLoadChange" @syncUnitInfoForm="syncUnitInfoForm" @notifyValidate="notifyValidate"></router-view>
        <!-- 加载页 -->
        <div class="unit-loading" v-show="getUnitLoading" v-loading="getUnitLoading"></div>
        <!-- 通知组件 -->
        <LgNotification
          :class="[singleGenerate ? 'notification-single' : 'notification-batch', isMobile ? 'mobile-notification' : '']"
          ref="notification"
          :position="position"
          :showCloseButton="true"
          :autoClose="false"
          :timer="progressTimer"
          :progress="Number(progress)"
          @close="() =>$analytics.sendEvent('web_unit_create3_det_meter_close')"
        >
          <span slot="content">
            <span v-if="singleGenerate">🚀 {{ $t('loc.generatingLesson') }}</span>
            <span v-else>
              <div class="scrolling-box">
                <div :class="isEsLang ? 'scrolling-text2' : 'scrolling-text'">🚀 {{ $t('loc.generatingLessons') }}</div>
                <div class="scrolling-text2">⌛ {{ $t('loc.generatingLessons2') }}</div>
                <div :class="isEsLang ? 'scrolling-text2' : 'scrolling-text'">🚀 {{ $t('loc.generatingLessons') }}</div>
                <div class="scrolling-text2">⌛ {{ $t('loc.generatingLessons2') }}</div>
              </div>
            </span>
          </span>
        </LgNotification>
    </div>
</template>

<script>
import tools from '@/utils/tools'
import { mapState } from 'vuex'
import Lessons2 from '@/api/lessons2'
import LgNotification from '@/components/LgNotification.vue'
import {equalsIgnoreCase, equalsNotIgnoreCase} from '@/utils/common'
import '@/assets/css/mobile-responsive.less'
import UnitInfoForm from './components/editor/UnitInfoForm.vue'
import LgTabs from '@/components/LgTabs'
import { removeUnexpectedCharacters } from '@/utils/eventSource'

export default {
    components: {
      LgNotification,
      UnitInfoForm,
      LgTabs
    },
    data () {
        return {
            getUnitLoading: false, // 获取单元详情 loading
            rosterPath: this.$router.resolve({ name: 'roster' }).href, // Roster 页面路由
            batchTaskTimer: null, // 批量生成任务定时器
            position: '', // 通知位置
            progress: 100, // 进度
            batchTaskCompleted: false, // 批量任务是否完成
            progressTimer: null, // 进度条定时器
            limitLoopCount: 0, // 限制轮询次数
            lang: 'en-US', // 系统当前语言
            plannerDialogShow: false, // 单元预览开关
            isMobile: false, // 是否是移动端
            showPromptPanel: false, // 控制 Prompt 弹窗显示/隐藏
            editPromptVisible: false, // 是否显示编辑提示弹窗
            currentRouteComponent: null, // 当前路由组件引用
            showLeftPanel: true, // 控制左侧面板显示/隐藏状态
            promptLoading: false, // 控制 Prompt 加载状态
            promptBtnTop: null, // 添加 prompt 按钮的位置记录
            popoverTopClass: '', // 用于控制 popover 位置的 class 名
            autoRetryCount: 0, // 自动重试次数
            lightAdapt: false, // 轻量改编特殊处理
            activeTab: 'unitFoundations', // 轻量改编时激活的 tab
            lightAdaptTabs: [{
              label: 'Unit Foundations',
              name: 'unitFoundations'
            },
              {
                label: 'Weekly Planning',
                name: 'weeklyPlanning'
              }],
            itemId: null // 课程 ID
        }
    },
    created () {
        // 设置语言
        this.lang = tools.localItem('NG_TRANSLATE_LANG_KEY') || 'en-US'
        // 获取单元数据
        this.getUnit()
        this.getUserFeatureGuide()
        // 检测设备类型
        this.checkDeviceType()
        // 监听窗口大小变化
        window.addEventListener('resize', this.checkDeviceType)
    },
    computed: {
        ...mapState({
            isCG: state => state.curriculum.isCG, // 是否是 Curriculum Genie
            isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
            unitId: state => state.curriculum.unit.id, // 单元 ID
            baseInfo: state => state.curriculum.unit.baseInfo, // 单元基本信息
            batchId: state => state.unit.batchId, // 批量生成任务 ID
            batchTasks: state => state.unit.batchTasks, // 批量生成任务
            appearNotice: state => state.unit.appearNotice, // 是否显示进度通知
            currentUser: state => state.user.currentUser,
            singleGenerate: state => state.unit.singleGenerate, // 单个生成任务
            unit: state => state.curriculum.unit, // 单元
            unitHasUpdated: state => state.curriculum.unitHasUpdated, // 单元内容是否更新
            isGenieCurriculumToUnitPlanThird: state => state.curriculum.isGenieCurriculumToUnitPlanThird
        }),
        // 是否是西班牙语
        isEsLang () {
          return this.lang === 'es-ES'
        },
        maxRetryCount: {
          get () {
            return this.$store.state.unit.maxRetryCount
          },
          set (value) {
            this.$store.dispatch('unit/setResetMaxRetry', value)
          }
        },
        weeklyPlans: {
          get () {
            return this.$store.state.curriculum.unit.weeklyPlans
          },
          set (value) {
            this.$store.commit('curriculum/SET_WEEKLY_PLANS', value)
          }
        },
        // 是否显示步骤
        showSteps () {
            const path = this.$route.path
            return path.includes('/weekly-plan-overview') || path.includes('/lesson-overview') || path.includes('/unit-overview')
        },
        // 当前步骤
        step () {
            const path = this.$route.path
            if (path.includes('/unit-overview')) {
                return 0
            } else if (path.includes('/weekly-plan-overview')) {
                return 1
            } else if (path.includes('/lesson-overview')) {
                return 2
            }
            return 0
        },
        // 返回标题
        backTitle () {
            const path = this.$route.path
            if (path.includes('/weekly-plan-overview')) {
                return 'Unit Overview'
            } else if (path.includes('/lesson-overview')) {
                return this.$t('loc.unitPlannerStep1WeeklyTheme')
            } else if (path.includes('/lesson-detail')) {
                return 'Lesson Plan Ideas'
            }
            return 'Back'
        },
        // Unit Planner 上内边距
        paddingTop () {
          if (this.$route.fullPath.indexOf('/lesson-detail/') > -1) {
            return '8px'
          } else if (this.$route.fullPath.indexOf('/lessons/unit-planner/unit/') > -1) {
            return '0px'
          } else {
            return '20px'
          }
        },
        // 显示 Roster
        showRosterBtn () {
            const path = this.$route.path
            return path.includes('/lesson-detail')
        },
        // 展示 Prompt 按钮
        showPromptBtn() {
            // 只有当左侧面板不显示时才显示 Prompt 按钮
            return !this.showLeftPanel
        },
        isShowPromptPanel() {
          return !this.showPromptPanel
        },
        // 是否是单元导入
        isAdaptImportUnit() {
          if (this.$route.path) {
            return this.$route.path.includes("/curriculum-genie/unit-adapt")
          }
          return false
        },
    },
    watch: {
      unitId (unitId) {
        if (unitId) {
          this.getUnitGenerateLessonTask()
        }
      },
      batchId: {
        immediate: true,
        handler(batchId) {
          if(batchId) {
            this.$nextTick(() => {
              this.$refs.notification.show()
              this.queryBatchTask()
            })
          } else {
            // 如果 batchId 已经不存在了，那么就直接设置进度条为 0
            this.progress = 0
          }
        }
      },
      batchTasks: {
        deep: true,
        handler (val) {
          if (this.batchId && !val) {
            this.progress = 100
          } else {
            const pendingTasks = val.pendingTasks || []
            const processingTasks = val.processingTasks || []
            const completedTasks = val.completedTasks || []
            const failedTasks = val.failedTasks || []
            // 总数
            const total = pendingTasks.length + processingTasks.length + completedTasks.length + failedTasks.length
            // 完成数
            const completed = completedTasks.length
            // 计算结果保留两位小数
            this.progress = ((total - completed) / total * 100).toFixed(2)
          }
        }
      },
      progress (value) {
        if (value <= 0) {
          this.$refs.notification.close()
          this.$store.dispatch('unit/setAppearNotice', false)
        }
      },
      appearNotice () {
        if (this.appearNotice) {
          this.progress = 100
          this.$refs.notification.show()
        } else {
          if (!this.singleGenerate) {
            this.$refs.notification.close()
            clearTimeout(this.batchTaskTimer)
          }
        }
      },
      '$route': {
        deep: true,
        immediate: true,
        handler () {
          // 判断当前页面位置
          const path = this.$route.fullPath
          // 如果是单元概览页面，则通知位置在右上角
          if (path.indexOf('/lesson-overview/') !== -1) {
            this.position = 'top-right'
          } else if (path.indexOf('/lesson-detail/') !== -1) {
            // 如果是课程详情页面，则通知位置在左上角
            this.position = 'top-left'
          }
          // 如果切换页面，批量任务还在进行中，则需要继续显示通知
          if (this.batchTaskTimer && this.batchId && (path.indexOf('/lesson-overview/') !== -1 || path.indexOf('/lesson-detail/') !== -1)) {
            this.$refs.notification.show()
          }
          // 设置是否是 Curriculum Genie 课程设计
          let curriculumGenieDesignerRouteNames = ['lesson-overview-cg-designer','lesson-detail-cg-designer']
          let isGenieCurriculumToUnitPlanThird = curriculumGenieDesignerRouteNames.includes(this.$route.name)
          this.$store.commit('curriculum/SET_GENIE_CURRICULUM_TO_UNIT_PLAN_THIRD', isGenieCurriculumToUnitPlanThird)
          this.getUnit()
        }
      },
      singleGenerate (value) {
        if (value) {
          this.progress = 100
          this.$refs.notification.show()
          this.progressTimer = 30
          setTimeout(() => {
            this.progress = 30
          }, 3000)
        } else {
          this.progressTimer = 2
          this.progress = 0
          setTimeout(() => {
            this.$refs.notification.close()
            this.progressTimer = null
            this.progress = 100
          }, 2800)
        }
      }
    },
    methods: {
        // 轻量改编特殊处理
         initLightAdapt () {
          // 获取当前路由名称
          if (this.$route) {
            const routeName = this.$route.name
            if (equalsIgnoreCase(routeName, 'unit-overview-cg-adapt') && this.unit && equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
              this.lightAdapt = true
              this.activeTab = 'unitFoundations'
              // 从路由参数中获取 itemId
              let itemId = this.$route.params.itemId
              if (itemId) {
                this.itemId = itemId
              }
            }
            if (equalsIgnoreCase(routeName, 'edit-weekly-plan-overview-cg-adapt') && this.unit && equalsIgnoreCase(this.unit.adaptedType, 'LIGHT')) {
              this.lightAdapt = true
              this.activeTab = 'weeklyPlanning'
            }
          }
        },
        // 检测设备类型
        checkDeviceType() {
            this.isMobile = window.innerWidth <= 768
        },
        handlePromptPanel() {
          if (!this.showPromptPanel) {
            document.documentElement.style.setProperty('--popover-top', this.isMobile ? `0px` : `120px`)
          }
          this.showPromptPanel = !this.showPromptPanel
          // 展示 Prompt 弹窗埋点
          if (this.showPromptPanel) {
            this.$analytics.sendEvent('cg_unit_create_prompt_pop')
          }
        },
        // 处理左侧面板可见性变化事件
        handlePromptBtnVisibilityChange(isVisible) {
            this.showLeftPanel = isVisible
        },
        handlePromptPanelChange(isVisible) {
          this.showPromptPanel = isVisible
        },
        handlePromptLoadChange(isLoading) {
          this.promptLoading = isLoading
        },
        // 处理重新生成
        handleRegenerate() {
            const currentComponent = this.$refs.currentRouteComponent
            if (currentComponent && currentComponent.$options.name === 'UnitOverviewStep') {
              // 验证 UnitInfoForm 的表单
              currentComponent.syncUnitInfoForm(this.$refs.unitInfoForm)
              this.$refs.unitInfoForm.validate().then(() => {
                // 同步 UnitInfoForm 的设置信息
                currentComponent.generateUnitCustomOverviewValidate(false)
                // 发送重新生成埋点
                this.$analytics.sendEvent('cg_unit_create_prompt_click_regenerate')
              })
            }
        },
        notifySync() {
          const currentComponent = this.$refs.currentRouteComponent
          if (currentComponent && currentComponent.$options.name === 'UnitOverviewStep') {
              // 同步 UnitInfoForm 的设置信息
              currentComponent.syncUnitInfoForm(this.$refs.unitInfoForm)
          }
        },
        notifyValidate() {
          if (this.$refs.unitInfoForm) {
            this.$refs.unitInfoForm.validate().catch(() => {
              this.showPromptPanel = true
               // 未通过校验跳转到第一个错误的表单项
              setTimeout(() => {
                let formItemsWithError = this.$refs.unitInfoForm.$el.querySelectorAll('.is-error')
                // 过滤出可见的 dom
                formItemsWithError = tools.getVisibleDom(formItemsWithError)
                let firstItem = formItemsWithError[0]
                if (firstItem) {
                  firstItem.dispatchEvent(new CustomEvent('validate-failed', {}))
                  firstItem.scrollIntoView({ block: 'center' })
                }
              }, 0)
            })
          }
        },
        // 同步 UnitInfoForm 的设置信息
        syncUnitInfoForm(sourceUnitInfoForm) {
          this.$nextTick(() => {
            if (sourceUnitInfoForm && this.$refs.unitInfoForm) {
              this.$refs.unitInfoForm.syncUnitInfoForm(sourceUnitInfoForm)
            }
          })
        },
        // 获取用户引导功能
        getUserFeatureGuide () {
          const showBatchGenerateLessonTip = localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP')
          const showBatchGenerateLessonExitTip = localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_EXIT_TIP')
          if (!showBatchGenerateLessonTip || !showBatchGenerateLessonExitTip) {
            this.$axios.get($api.urls().getUserNeedGuideFeatures).then((result) => {
              if (result) {
                localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP', result.showBatchGenerateLessonTip)
                localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_EXIT_TIP', result.showBatchGenerateLessonExitTip)
              }
            })
          }
        },
        // 获取单元课程生成任务
        getUnitGenerateLessonTask () {
          Lessons2.getUnitGeneratingLessonTask(this.unitId).then(res => {
            // 如果存在任务，则需要将批量任务 ID 设置到 Vuex 中
            if (res && res.id) {
              this.showNotifition()
              this.$store.dispatch('unit/setBatchId', res.id)
            } else if (!(this.batchId && this.unit.adaptedType === 'LIGHT')) {
              // 如果没有批量任务，则需要将批量任务 ID 设置为 null
              this.$store.dispatch('unit/setBatchId', null)
              this.$store.dispatch('unit/setBatchTasks', null)
            }
          })
        },
        // 是否显示进度条通知栏
        showNotifition () {
          // 如果有正在生成的任务列表或者是点击了批量生成，才会显示进度条
          if (this.batchId || this.batchTasks) {
            this.$refs.notification.show()
          }
        },

        back () {
            const path = this.$route.path
            let backRoute = 'unit-list'
            if (path.includes('/weekly-plan-overview')) {
                backRoute = 'unit-overview'
            } else if (path.includes('/lesson-overview')) {
                backRoute = this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'weekly-plan-overview-cg' : 'weekly-plan-overview'
            } else if (path.includes('/lesson-detail')) {
                this.$router.push({
                    name: 'lesson-overview',
                    params: {
                        unitId: this.unitId,
                        week: 1
                    }
                })
                return
            }
            this.$router.push({
                name: backRoute
            })
        },

        // 加载单元数据
         getUnit () {
          
            // 获取路由参数 unitId
            const unitId = this.$route.params.unitId
            // 没有指定单元 ID 或者和当前已有 ID 相同则跳过
            if ((!unitId || unitId === this.unitId) && (!this.unit.adaptedType || (this.unit && this.unit.overview && this.unit.overview.concepts))) {
                this.initLightAdapt()
                return
            }
            this.getUnitLoading = true
            // 请求参数
            let params = {
                params: {
                    unitId: unitId,
                    edit: true
                }
            }
            // 获取单元详情
            this.$axios
                .get($api.urls().getUnitInfo, params).then((res) => {
                    if (res.lockedData) {
                        return
                    }
                    let unit = res.unit
                    // 单元基本信息
                    unit.baseInfo = {
                        title: unit.title, // 标题
                        number: unit.number, // 编号
                        country: unit.country || 'United States', // 国家
                        state: unit.state, // 状态
                        city: unit.city, // 城市
                        description: unit.description, // 描述
                        frameworkId: unit.frameworkId, // 框架 ID
                        frameworkName: unit.frameworkName, // 框架名称
                        domainIds: unit.domainIds, // 领域 ID
                        allDomains: unit.options, // 选项
                        grade: unit.grade, // 年级
                        language: unit.language, // 语言
                        weekCount: unit.weekCount, // 周数
                        progress: unit.progress, // 进度
                        rubrics: unit.rubrics, // 校训
                        newRubrics: unit.newRubrics, // 新校训
                        useLocation: unit.useLocation, // 是否显示国家城市
                        useDomain: unit.useDomain, // 是否按领域选择
                        measureIds: unit.measureIds, // measure ID
                        useLessonTemplate: unit.useLessonTemplate, // 是否使用课程模板
                        showLessonTemplateTip: unit.showLessonTemplateTip, // 是否显示课程模板提示
                        classroomType: unit.classroomType || 'IN_PERSON', // 课堂类型：IN_PERSON 或 VIRTUAL
                        anchorText: unit.anchorText, // Anchor Text
                        readingLevelSetting: unit.readingLevelSetting, // 阅读能力设置
                        activityCount: unit.activityCount, // 活动数量
                        adaptedType: unit.adaptedType, // 单元导入改编类型
                        adaptedModuleSwitch: unit.adaptedModuleSwitch, // 改编模块开关
                        measures: unit.measures.map(measure => {
                            return {
                              id: measure.id,
                              abbreviation: measure.abbreviation,
                              name: measure.name
                            };
                          }) // measures 已选测评点

                    }
                    // 单元概览信息
                    unit.overview = {
                        title: unit.title, // 标题
                        coverMedias: unit.coverMedias || [],
                        coverKeywords: unit.coverKeywords, // 封面搜索关键词
                        overview: unit.overview, // 概览
                        concepts: unit.concepts, // 概念
                        guidingQuestions: unit.guidingQuestions, // 指导问题
                        trajectory: unit.trajectory // 轨迹
                    }
                    unit.customFoundationInfos = res.unit.customFoundationInfos
                    unit.weekLessonCountMap = res.weekLessonCountMap // 周内课程数量 (单元导入深度改编时）
                    unit.adaptedType = res.unit.adaptedType // 单元导入改编类型
                    unit.adaptedModuleSwitch = res.unit.adaptedModuleSwitch
                    // 构建 customFoundationData
                    unit.customFoundationData = {}
                    if (unit.customFoundationInfos && unit.customFoundationInfos.length > 0) {
                      unit.customFoundationInfos.forEach((item, index) => {
                        unit.customFoundationData[item.key] = item.content
                      })
                    }
                    this.$store.commit('curriculum/SET_UNIT', unit)
                        // 设置扩展参数
                        this.$store.dispatch('curriculum/setPromptExtensionParams', {
                            unitId: unit.id
                        })
                        // 单元周计划需要初始化
                        this.getUnitLoading = false
                        this.initLightAdapt()
                    }).catch(error => {
                        this.getUnitLoading = false
                        console.log(error)
                        if (!equalsNotIgnoreCase(error.response.data.error_message, "LESSON_NOT_FOUND")) {
                          this.$message.error(error.response.data.error_message)
                        }
                    })
        },
        // 查询批量生成任务
        queryBatchTask () {
          // 校验参数, 如果没有 batchId 则不进行数据获取
          if (!this.batchId) {
            return
          }
          const processBatchTaskFun = () => {
            Lessons2.getBatchGenerateLessonTasks(this.batchId).then(res => {
              // 如果批量任务不存在，则直接返回
              if (!this.batchId) {
                return
              }
              // 如果任务未完成，则获取任务进度
              const tasks = {
                pendingTasks: res.pendingTasks,
                processingTasks: res.processingTasks,
                completedTasks: res.completedTasks,
                failedTasks: res.failedTasks
              }
              // 如果任务已经完成，则直接返回
              if (res.completed) {
                // 如果任务列表中存在失败或待处理的任务，且最大重试次数小于 3，则进行重试
                if ((res.failedTasks.length > 0 || res.pendingTasks.length > 0) && this.maxRetryCount < 3) {
                  this.retryGenerateLessonTask(true)
                } else {
                  this.$message.success(this.$t('loc.batchGeneratedSuccessfully'))
                }
                // 清除定时器，解放轮询
                clearTimeout(this.batchTaskTimer) // 清除定时器
                // 判断课程是否生成成功，如果任务列表中生成成功的课程不为空，则设置课程 ID
                if (res.completedTasks.length > 0) {
                  this.setCompletedLessonsIds(res.completedTasks)
                }
                // 生成任务完成后，清空批量任务
                this.$store.dispatch('unit/setBatchTasks', tasks)
                this.$store.dispatch('unit/setAppearNotice', false)
                // 如果任务列表中失败或待处理的任务为空，且已经重试了 3 次，则清空批量生成任务 ID
                if ((res.failedTasks.length === 0 && res.pendingTasks.length === 0) || this.maxRetryCount === 3) {
                  this.$store.dispatch('unit/setBatchId', null)
                }
                // 进度降为 0
                this.progress = 100
                // 修改课程生成状态
                this.updateGenerateProcess(true)
                return
              }
              // 限制轮询次数
              this.limitLoopCount++
              let time = 10000
              // 前三次轮询时间为 3 秒，之后每次轮询时间为 10 秒
              if (this.limitLoopCount > 3) {
                time = 3000
              }
              this.$store.dispatch('unit/setBatchTasks', tasks)
              // 如果出现待处理的任务不为空，则将待处理的进行重试
              if (res.pendingTasks.length > 0) {
                this.retryGenerateLessonTask()
              }
              // 修改课程生成状态
              this.updateGenerateProcess()
              // 轮询执行任务
              this.batchTaskTimer = setTimeout(processBatchTaskFun, time)
            }).catch(() => {})
          }
          // 执行任务
          processBatchTaskFun()
        },
        // 为生成成功的课设置课程 ID
        setCompletedLessonsIds (completedTasks) {
          completedTasks.forEach(task => {
            this.weeklyPlans.forEach(weeklyPlan => {
              weeklyPlan.items.forEach(item => {
                // 如果当前的活动项存在课程 ID 则不需要再次设置
                if (item.lessonId) {
                  return
                }
                if (equalsIgnoreCase(task.itemId, item.id)) {
                  this.$set(item, 'lessonId', task.lessonId)
                }
              })
              weeklyPlan.centerItems.forEach(item => {
                // 如果当前的活动项存在课程 ID 则不需要再次设置
                if (item.lessonId) {
                  return
                }
                if (equalsIgnoreCase(task.itemId, item.id)) {
                  this.$set(item, 'lessonId', task.lessonId)
                }
              })
            })
          })
          const failedTasks = this.batchTasks.failedTasks
          const pendingTasks = this.batchTasks.pendingTasks
          const processingTasks = this.batchTasks.processingTasks
          // 设置成功之后需要将成功的任务清除
          this.$store.dispatch('unit/setBatchTasks', {
            pendingTasks,
            processingTasks,
            completedTasks: [],
            failedTasks
          })
        },
      updateGenerateProcess (completed = false) {
          // 获取任务各种状态的 ItemId
          const completedTaskIds = this.batchTasks.completedTasks.map(task => task.itemId)
          const failedTaskIds = this.batchTasks.failedTasks.map(task => task.itemId)
          const pendingTaskIds = this.batchTasks.pendingTasks.map(task => task.itemId)
          const processingTaskIds = this.batchTasks.processingTasks.map(task => task.itemId)
          this.weeklyPlans.forEach(weeklyPlan => {
            // 如果任务结束，则非成功的任务都设置为失败
            if (completed) {
              weeklyPlan.items.forEach(item => {
                this.$set(item, 'confirmed', true)
                // 如果任务结束，则非成功的任务都设置为失败
                if (completedTaskIds.includes(item.id) || failedTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                  // 如果彻底失败则将 error 设置为 true
                  if (failedTaskIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                } else if (!completedTaskIds.includes(item.id) && !failedTaskIds.includes(item.id) && !pendingTaskIds.includes(item.id) && !processingTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                } else {
                  // 如果任务结束，重试三次之后依旧失败，则设置为失败
                  if (this.maxRetryCount === 3) {
                    this.$set(item, 'processing', false)
                  } else {
                    this.$set(item, 'processing', true)
                  }
                }
              })
              weeklyPlan.centerItems.forEach(item => {
                this.$set(item, 'confirmed', true)
                if (completedTaskIds.includes(item.id) || failedTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                  // 如果彻底失败则将 error 设置为 true
                  if (failedTaskIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                } else if (!completedTaskIds.includes(item.id) && !failedTaskIds.includes(item.id) && !pendingTaskIds.includes(item.id) && !processingTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                } else {
                  // 如果任务结束，重试三次之后依旧失败，则设置为失败
                  if (this.maxRetryCount === 3) {
                    this.$set(item, 'processing', false)
                  } else {
                    this.$set(item, 'processing', true)
                  }
                }
              })
            } else {
              // 如果任务未结束，则根据任务状态设置任务状态，成功或失败的将 processing 设置为 false，其他的设置为 true
              weeklyPlan.items.forEach(item => {
                this.$set(item, 'confirmed', true)
                if (completedTaskIds.includes(item.id) || failedTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                  // 如果彻底失败则将 error 设置为 true
                  if (failedTaskIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                } else if (processingTaskIds.includes(item.id) || pendingTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', true)
                }
              })
              weeklyPlan.centerItems.forEach(item => {
                this.$set(item, 'confirmed', true)
                if (completedTaskIds.includes(item.id) || failedTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', false)
                  // 如果彻底失败则将 error 设置为 true
                  if (failedTaskIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                } else if (processingTaskIds.includes(item.id) || pendingTaskIds.includes(item.id)) {
                  this.$set(item, 'processing', true)
                }
              })
            }
          })
        },
        // 重试生成任务
        retryGenerateLessonTask (completed = false) {
          // 如果轮询任务已经完成，且有失败的任务并且重试次数小于 3 次，则进行重试
          if (completed && (this.batchTasks.failedTasks.length > 0 || this.batchTasks.pendingTasks.length > 0) && this.maxRetryCount < 3 && this.autoRetryCount > 5) {
            this.maxRetryCount++
            this.$confirm(this.$t('loc.batchLessonFailed'), 'Confirmation', {
              confirmButtonText: this.$t('loc.batchLessonFailedRetry'),
              cancelButtonText: 'Cancel'
            }).then(() => {
              // 收集 taskId 重新执行任务
              const failTaskIds = this.batchTasks.failedTasks.map(task => task.taskId)
              const pendingTaskIds = this.batchTasks.pendingTasks.map(task => task.taskId)
              const allTaskIds = failTaskIds.concat(pendingTaskIds).join(',')
              // 如果存在失败的任务 ID，则重新生成任务
              if (allTaskIds) {
                Lessons2.batchGenerateTasks(allTaskIds).then(res => {
                  this.$store.dispatch('unit/setAppearNotice', true)
                  this.queryBatchTask()
                })
              }
            }).catch(() => {
              // 将批量任务取消
              this.$store.dispatch('unit/setBatchId', null)
              this.$store.dispatch('unit/setResetMaxRetry', 0)
              this.$store.dispatch('unit/setBatchTasks', null)
              this.$store.dispatch('unit/setAppearNotice', false)
              // 取消重试任务将失败的 item 的生成失败状态都设置为 true
              const failedTaskItemIds = this.batchTasks.failedTasks.map(task => task.itemId)
              this.weeklyPlans.forEach(weeklyPlan => {
                weeklyPlan.items.forEach(item => {
                  if (failedTaskItemIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                })
                weeklyPlan.centerItems.forEach(item => {
                  if (failedTaskItemIds.includes(item.id)) {
                    this.$set(item, 'error', true)
                  }
                })
              })
            })
          } else {
            const failTaskIds = this.batchTasks.failedTasks.map(task => task.taskId)
            let pendingTaskIds = this.batchTasks.pendingTasks.map(task => task.taskId)
            const allTaskIds = failTaskIds.concat(pendingTaskIds).join(',')
            // 如果存在失败的任务 ID，则重新生成任务
            if (allTaskIds) {
              // 如果任务完成，但是有失败的需要自动重试，自动重试次数加 1
              if (completed) {
                this.autoRetryCount++
              }
              Lessons2.batchGenerateTasks(allTaskIds)
            }
          }
        },
        // 添加监听滚动事件的方法
        handleScroll() {
          // 获取 prompt 按钮元素
          const promptBtn = document.querySelector('.prompt-btn')
          if (!promptBtn) return

          const rect = promptBtn.getBoundingClientRect()
          const isVisible = rect.top > 0 && rect.bottom <= window.innerHeight

          if (isVisible) {
            // 如果按钮可见，更新位置
            this.promptBtnTop = rect.top
            this.updatePopoverPosition(rect.top)
          } else if (this.promptBtnTop !== null) {
            // 如果按钮不可见但之前记录过位置，保持最后的位置
            this.updatePopoverPosition(0)
          }
        },

        // 更新 popover 位置的方法
        updatePopoverPosition(top) {
          // 使用 CSS 变量设置位置
          document.documentElement.style.setProperty('--popover-top', `${top + 40}px`)
        },
        // 轻量改编返回按钮点击事件
        handleLightAdaptBack() {
          if (this.itemId) {
             // 点击返回课程详情页
             this.$analytics.sendEvent('cg_adapt_foundation_back_clicked')
            this.$router.push({name: 'lesson-detail-cg-adapt',params: {unitId: this.unitId, itemId: this.itemId}})
            this.lightAdapt = false;
          } else {
            this.$axios.get($api.urls().getUnitNavigationData, {
              params: {
                unitId: this.unitId
              }
            }).then(res => {
              // 如果没有获取到数据，则跳转到课程概览
                if (!res || !res.unitId || !res.weeklyPlans || res.weeklyPlans.length === 0) {
                  return
                }
                // 获取 itemId：优先从 items 中获取，如果不存在则从 centerItems 中获取
                const weeklyPlan = res.weeklyPlans[0]
                let itemId = null
                if (weeklyPlan.items && weeklyPlan.items.length > 0) {
                  itemId = weeklyPlan.items[0].id
                } else if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
                  itemId = weeklyPlan.centerItems[0].id
                  this.itemId = itemId
                }
                // 点击返回课程详情页
                this.$analytics.sendEvent('cg_adapt_foundation_back_clicked')
                this.$router.push({name: 'lesson-detail-cg-adapt',params: {itemId: itemId}})
                this.lightAdapt = false;
            })
          }
        },
        // 处理 Tab 切换
        handleTabChange(tab) {
          // 更新当前标签页
          this.activeTab = tab.name;
          if (tab.name === 'unitFoundations') {
            // 点击进入 Unit Foundations 编辑页
            this.$analytics.sendEvent('cg_adapt_foundation_page_entered')
            this.$router.push({name: 'unit-overview-cg-adapt',params: {unitId: this.unitId}})
          } else if (tab.name === 'weeklyPlanning') {
             // 点击进入 Weekly Planning 编辑页
             this.$analytics.sendEvent('cg_adapt_weekly_page_entered')
            this.$router.push({name: 'edit-weekly-plan-overview-cg-adapt',params: {unitId: this.unitId}})
          }
        }
    },
    mounted () {
      // 添加滚动监听
      if (!this.isMobile) {
        window.addEventListener('scroll', this.handleScroll, true)
      }
    },
    destroyed () {
      if (this.baseInfo && this.unitId) {
        this.$axios.post($api.urls().unlockUnit, {}, { params: { unitId: this.unitId } })
        // 如果单元内容有改变，那么进行操作记录的保存
        if (this.unitHasUpdated && this.unit.id) {
          Lessons2.addAction({ unitId: this.unit.id }).catch(err => {
          })
        }
      }
      // 清除单元内容已修改的标记
      this.$store.commit('curriculum/SET_UNIT_HAS_UPDATED', false)
      // 清空批量生成任务 ID
      this.$store.dispatch('unit/setBatchId', null)
      // 清空批量生成任务
      this.$store.dispatch('unit/setBatchTasks', null)
      // 重置最大重试次数
      this.$store.dispatch('unit/setResetMaxRetry', 0)
      // 清空单元
      this.$store.commit('curriculum/RESET_UNIT')
      // 清除定时器
      clearTimeout(this.batchTaskTimer)
      // 移除窗口大小变化监听
      window.removeEventListener('resize', this.checkDeviceType)
      // 移除滚动监听
      window.removeEventListener('scroll', this.handleScroll, true)
    }
}
</script>

<style lang="less" scoped>
.unit-steps {
    width: 700px;
    margin: 10px auto;
    margin-top: -10px;
}
.unit-loading {
    width: 100%;
    height: calc(100vh - 100px);
}
::v-deep {
    .operation-panel {
        // width: 500px;
        flex-shrink: 0;
        position: sticky;
        top: 0;
        height: max-content;
    }
}

.notification-single {
    width: 400px;
    line-height: 40px;
    font-size: 16px;
    /deep/ .content {
        padding: 16px 12px 9px;
    }
}

.notification-batch {
  width: 500px;
  line-height: 60px;
  font-size: 16px;
  /deep/ .content {
    padding: 16px 12px 9px;
  }
}

.mobile-notification {
  width: 90% !important;
  max-width: 350px;
  font-size: 14px;
}

@keyframes scroll {
    0% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-48px);
    }
    50% {
        transform: translateY(-48px);
    }
    75% {
        transform: translateY(-96px);
    }
    100% {
        transform: translateY(-96px);
    }
}

/* 滚动容器样式 */
.scrolling-box {
  overflow: hidden;
  height: 48px;
  line-height: 24px;
}

/* 文字滚动样式 */
.scrolling-text {
  animation: scroll 12s infinite;
  line-height: 48px;
  height: 48px;
  word-break: break-word;
}
.scrolling-text2 {
  animation: scroll 12s infinite;
  line-height: 24px;
  height: 48px;
  word-break: break-word;
}

.prompt-btn-container {
  width: auto;
  padding-left: 24px;
}

.prompt-title {
  color: #111c1c;
  padding:0 10px;
  padding-bottom: 10px;
  margin: 0 -10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.steps-container {
  flex: 1;
  display: flex;
  justify-content: center;

  @media screen and (max-width: 768px) {
    flex-direction: column-reverse;
  }
}


.prompt-dialog {
  /deep/ .el-dialog__header {
    display: none;
  }
  /deep/ .el-dialog__body {
    padding: 20px;
  }

  .dialog-header {
    margin-bottom: 20px;
  }
}

.unit-info-form-scroll {
  height: calc(100% - 95px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
    border-radius: 3px;
  }
}

.light-adapt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 10;
  height: 60px;
  width: 100%;
}

.back-button {
  font-size: 16px;
  color: #10B3B7;
  padding: 8px 0;
  height: auto;
  line-height: normal;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;

  &:hover {
    color: #0ea5a9;
    background: none;
  }

  &:focus {
    color: #10B3B7;
    background: none;
    border: none;
  }

  .lg-icon {
    font-size: 20px;
  }
}
</style>

<style lang="less">
.steps-outer {
  position: sticky;
  top: -54px;
  z-index: 10;
}
.el-popover.desktop-popover {
  top: var(--popover-top) !important;
  position: fixed !important;
  width: 26vw !important;
  min-width: 400px !important;;
  height: calc(66% + 40px) !important;
  border-radius: 8px;
  padding-left: 16px;
  padding-right: 16px;
}
.el-popover.mobile-popover {
  top: 0 !important;
  left: 0 !important;
  height: 500px !important;
  margin-left: 0 !important;
  max-width: 100%;
  border-radius: 8px;
}
</style>