<template>
  <div class="unit-reading-level-setting">
    <div class="setting-left">
        <i class="el-icon-document"></i>
    </div>
    <div class="setting-right">
      <!-- 标题和描述 -->
      <div class="header-content">
        <div class="header-text">
            <h3 class="setting-title">Reading Passage Review & Differentiation</h3>
            <p class="setting-description">Review the recommended resources for Anchor & Supporting Text, generate with AI or upload your own, and set reading levels for class differentiation.</p>
        </div>
        <div class="header-action">
          <el-button type="primary" :disabled="disabled" @click="handleSelectNow">Select Now</el-button>
          <span class="new-badge">New</span>
        </div>
      </div>
      <!-- 阅读等级设置区域 -->
      <div class="reading-level-section">
          <div class="level-label">
            <span>Reading Level</span>
            <el-tooltip content="Select reading level for differentiation" placement="top">
                <i class="lg-icon lg-icon-question"></i>
            </el-tooltip>
          </div>
          
          <!-- 等级选择器 -->
          <div class="level-selector-container">
          <!-- 年级模式 -->
          <div v-if="currentMode === 'GRADE'" class="grade-mode">
              <div class="select-wrapper">
                <!-- 年级选择器 -->
                <el-select
                    v-model="selectedGradeValues"
                    multiple
                    filterable
                    :disabled="disabled"
                    class="level-select"
                    @change="handleGradeSelectChange"
                    @remove-tag="handleGradeRemoveTag"
                    @clear="handleGradeClear"
                >
                    <el-option
                    v-for="grade in gradeOptions"
                    :key="grade.value"
                    :label="grade.label"
                    :value="grade.value"
                    class="level-option"
                    >
                    <span class="option-text">{{ grade.label }}</span>
                    </el-option>
                </el-select>
              </div>
          </div>
  
          <!-- 蓝思值模式 -->
          <div v-else-if="currentMode === 'LEXILE'" class="lexile-mode">
            <div class="select-wrapper">
              <!-- 蓝思值选择器 -->
              <el-select
                v-model="selectedLexileValues"
                multiple
                filterable
                :disabled="disabled"
                class="level-select"
                @change="handleLexileSelectChange"
                @remove-tag="handleLexileRemoveTag"
                @clear="handleLexileClear"
              >
                <el-option
                  v-for="lexile in lexileOptions"
                  :key="lexile.value"
                  :label="lexile.label"
                  :value="lexile.value"
                  class="level-option"
                >
                  <span class="option-text">{{ lexile.label }}</span>
                </el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="mode-selector">
          <el-radio-group v-model="currentMode" @change="handleModeChange" :disabled="disabled" class="mode-radio-group">
            <el-radio label="GRADE" class="mode-radio">
              <span class="radio-label">By grade</span>
            </el-radio>
            <el-radio label="LEXILE" class="mode-radio">
              <span class="radio-label">By lexile</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { TAILOR_UNIT_AGE_GROUPS, LEXILE_LEVELS } from '@/utils/const'
import { mapState } from 'vuex'
import tools from '@/utils/tools'

export default {
  name: 'UnitReadingLevelSetting',
  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapState({
      unit: state => state.curriculum.unit
    }),
    baseInfo: {
      get () {
        return this.$store.state.curriculum.unit.baseInfo
      },
      set (value) {
        this.$store.commit('curriculum/SET_BAE_INFO', value)
      }
    },
    unitGrade() {
      return this.baseInfo.grade
    },
    unitId() {
      return this.unit.id
    },
    readingLevelSetting() {
      return this.baseInfo && this.baseInfo.readingLevelSetting
    }
  },
  data() {
    return {
      // 当前模式：grade 或 lexile
      currentMode: 'GRADE',
      // 年级选项
      gradeOptions: [],
      // 蓝思值选项
      lexileOptions: [],
      // 选中的年级值数组
      selectedGradeValues: [],
      // 选中的蓝思值数组
      selectedLexileValues: []
    }
  },

  watch: {
    readingLevelSetting () {
      this.updateSelectedValues()
    }
  },
  mounted() {
    this.updateUnitReadingLevel = tools.debounce(this.updateUnitReadingLevelHandler, 5000)
    this.initOptions()
  },
  methods: {
    // 更新阅读能力设置
    updateUnitReadingLevel: null,
    // 现在选择按钮
    handleSelectNow() {
      this.$emit('selectNow')
    },
    // 初始化选项数据
    initOptions() {
      // 初始化年级选项
      this.gradeOptions = TAILOR_UNIT_AGE_GROUPS.map(grade => ({
        value: grade.ageGroup,
        label: grade.label
      }))

      // 初始化蓝思值选项
      this.lexileOptions = LEXILE_LEVELS

      this.updateSelectedValues()
    },

    // 更新选中的值数组
    updateSelectedValues() {
      if (this.readingLevelSetting) {
        this.currentMode = this.readingLevelSetting.mode || 'GRADE'
        if (this.currentMode === 'GRADE') {
          this.selectedGradeValues = this.readingLevelSetting.levels
        } else {
          this.selectedLexileValues = this.readingLevelSetting.levels
        }
      }
    },

    // 处理模式切换
    handleModeChange(mode) {
      this.currentMode = mode
      if (mode === 'GRADE') {
        this.selectedGradeValues = [this.unitGrade]
      } else {
        this.selectedLexileValues = []
      }
      this.emitChange()
    },

    // 处理年级选择变化
    handleGradeSelectChange(values) {
      this.selectedGradeValues = values
      this.emitChange()
    },

    // 处理年级移除标签
    handleGradeRemoveTag(value) {
      this.selectedGradeValues = this.selectedGradeValues.filter(v => v !== value)
      this.emitChange()
    },

    // 处理年级清除
    handleGradeClear() {
      this.selectedGradeValues = []
      this.emitChange()
    },


    // 处理蓝思值选择变化
    handleLexileSelectChange(values) {
      this.selectedLexileValues = values
      this.emitChange()
    },

    // 处理蓝思值移除标签
    handleLexileRemoveTag(value) {
      this.selectedLexileValues = this.selectedLexileValues.filter(v => v !== value)
      this.emitChange()
    },

    // 处理蓝思值清除
    handleLexileClear() {
      this.selectedLexileValues = []
      this.emitChange()
    },

    // 发射变化事件
    emitChange() {
      const value = {
        mode: this.currentMode,
        levels: this.currentMode === 'GRADE' ? this.selectedGradeValues : this.selectedLexileValues
      }
      this.updateUnitReadingLevel()
    },

    // 更新 unit
    async updateUnitReadingLevelHandler() {
      const readingLevel = {
        mode: this.currentMode,
        levels: this.currentMode === 'GRADE' ? this.selectedGradeValues : this.selectedLexileValues
      }
      const params = {
        unitId: this.unitId,
        readingLevel: readingLevel
      }
      await this.$axios.post($api.urls().updateUnitReadingLevel, params)
      this.baseInfo = { ...this.baseInfo, readingLevelSetting: readingLevel }
    }
  }
}
</script>

<style lang="scss" scoped>
.unit-reading-level-setting {
  background: #ffffff;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 16px 14px;
  margin-bottom: 24px;
  display: flex;
  gap: 24px;
  color: #111c1c;

  .setting-left {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #10b3b7, #10b3b7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    i {
      color: white;
      font-size: 24px;
    }
  }

  // 设置头部
  .setting-right {
    width: 100%;

    .header-content {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .header-text {
        flex: 1;

        .setting-title {
          font-size: 20px;
          font-weight: 600;
          margin: 0 0 8px 0;
          line-height: 1.4;
        }

        .setting-description {
          font-size: 14px;
          margin: 8px 0;
          line-height: 1.5;
        }
      }

      .header-action {
        position: relative;
        flex-shrink: 0;

        .new-badge {
          position: absolute;
          top: -8px;
          right: -8px;
          background: #ef4444;
          color: white;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: 10px;
          text-transform: uppercase;
        }
      }
    }

    // 阅读等级设置区域
    .reading-level-section {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-top: 16px;
      
      .level-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 500;
        white-space: nowrap;
        flex-shrink: 0;

        i {
          font-size: 16px;
          cursor: help;
          color: #9ca3af;
        }
      }
      
      .level-selector-container {
        flex: 1;
        min-width: 0; // 确保 flex 子元素可以收缩
        
        .select-wrapper {
          .level-select {
            width: 100%;
          }
        }
      }
      
      .mode-selector {
        flex-shrink: 0;

        .mode-radio-group {
          display: flex;
          gap: 24px;

          .mode-radio {
            .radio-label {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 分组设置
    .group-section {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #f3f4f6;

      .group-header {
        display: flex;
        align-items: center;
        gap: 12px;

        .group-indicator {
          width: 4px;
          height: 20px;
          background: linear-gradient(135deg, #4ade80, #22c55e);
          border-radius: 2px;
        }

        .group-title {
          font-size: 16px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
