<template>
  <div @click="openDialog">
    <!-- 自定义内容插槽 -->
    <slot name="content">
    </slot>
    
    <!-- 设置弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="900px"
      append-to-body
      @close="handleClose"
      custom-class="lesson-module-dialog"
      :close-on-click-modal="false"
    >
      <!-- 模块列表 - 两列网格布局 -->
      <template v-if="loading">
        <div>
          <el-skeleton-item variant="text" style="width: 300px; height: 20px;" />
        </div>
        <div class="lg-margin-top-8">
          <el-skeleton-item variant="button" style="width: 20px; height: 20px;" />
          <el-skeleton-item variant="text" style="width: 60px; height: 20px; margin-left: 10px;" />
        </div>
        <div class="module-grid">
          <div v-for="i in 11" :key="i" class="module-card skeleton">
            <el-skeleton-item variant="button" style="width: 20px; height: 20px;" />
            <el-skeleton-item variant="text" style="width: 150px; height: 20px; margin-left: 10px;" />
            <div class="module-info">
              <div>
                <el-skeleton-item variant="text" style="width: 300px; height:20px; margin-top: 8px;" />
              </div>
              <div>
                <el-skeleton-item variant="text" style="width: 200px;  height:20px; margin-top: 8px;" />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <!-- 描述内容 -->
        <div class="module-description">
          {{ description }}
        </div>

        <!-- 全选选项 -->
        <div>
          <el-checkbox 
            v-model="selectAll" 
            :indeterminate="isIndeterminate"
            @change="handleSelectAllChange"
            class="select-all-checkbox"
          >
            <span class="select-all-text">Select all</span>
          </el-checkbox>
        </div>
        <!-- 模块列表 -->
        <div class="module-grid">
          <div
            v-for="item in internalModuleList" 
            :key="item.id" 
            class="module-card"
            :class="{ 'disabled-module': !item.available }"
            @click="handleCardClick(item)"
          >
            <!-- 模块头部 -->
            <div class="module-header">
              <el-checkbox
                v-model="item.enabled"
                :indeterminate="item.id === 'clr' && isCLRIndeterminate(item)"
                @change="handleModuleChange(item)"
                @click.stop
                :disabled="!item.available || item.id === 'lesson_plan'"
                class="module-checkbox"
              >
                <span class="module-name">{{ item.name }}
                  <el-popover width="600" trigger="hover" placement="right">
                    <div class="module-info-popover">
                      <div class="title-font-16">{{ item.name }}</div>
                      <div class="title-font-14-regular lg-margin-top-bottom-8" v-html="item.popoverDescription"></div>
                      <div>
                        <!-- <img :src="require('@/assets/images/lesson-module-setting-guide/' + item.id + '.png')" alt="module-info-image" /> -->
                      </div>
                    </div>
                    <i slot="reference" class="lg-icon lg-icon-question" style="font-weight: 400;"></i>
                  </el-popover>
                </span>
              </el-checkbox>
              <i 
                class="el-icon-info-circle module-info-icon" 
                v-if="item.description"
                @click.stop="showModuleInfo(item)"
              ></i>
            </div>
            
            <!-- 模块描述 -->
            <div class="module-description-text" v-if="item.description">
              {{ item.description }}
            </div>
            
            <!-- CLR 特殊选项 -->
            <div v-if="item.id === 'clr'" class="clr-options">
              <el-checkbox-group v-model="item.clrOptions" @change="handleCLROptionsChange">
                <el-checkbox @click.stop label="0-TK">0-TK</el-checkbox>
                <el-checkbox @click.stop label="K-12">K-12</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </template>

      <!-- 自定义底部插槽 -->
      <template slot="footer">
        <slot name="footer">
          <div class="dialog-footer">
            <el-button @click="handleCancel" class="cancel-button" :disabled="loading || saveLoading">Cancel</el-button>
            <el-button type="primary" @click="handleSave" class="save-button" :loading="saveLoading" :disabled="loading">
              Save
            </el-button>
          </div>
        </slot>
      </template>
    </el-dialog>

    <!-- 模块信息提示 -->
    <el-tooltip
      v-model="moduleInfoVisible"
      :content="currentModuleInfo"
      placement="top"
      effect="dark"
      :manual="true"
      popper-class="module-info-tooltip"
    >
    </el-tooltip>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'LessonModuleSettings',
  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗标题
    title: {
      type: String,
      default: 'Customize Your Lesson Plan Modules'
    },
    // 弹窗描述
    description: {
      type: String,
      default: 'Please select the supporting components you want to include with your lesson plan.'
    },
    // 模块列表
    moduleList: {
      type: Array,
      default: () => []
    },
    // 设置埋点名称
    setupEventName: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState({
      lessonModuleSetting: state => state.lesson.lessonModuleSetting
    })
  },  
  data () {
    return {
      // 弹窗是否可见
      dialogVisible: false,
      // 是否在加载中
      loading: false,
      // 保存按钮 loading 状态
      saveLoading: false,
      // 全选状态
      selectAll: false,
      // 半选状态
      isIndeterminate: false,
      // 模块信息提示是否可见
      moduleInfoVisible: false,
      // 当前显示的模块信息
      currentModuleInfo: '',
      // 内部模块列表副本
      internalModuleList: [
        {
          id: 'lesson_plan',
          name: 'Lesson Plan',
          description: 'Core lesson plan components always included (e.g. objectives, materials, implementation steps, vocabulary words)',
          popoverDescription: 'Core lesson plan components always included (e.g. objectives, materials, implementation steps, vocabulary words)',
          enabled: true,
          available: false,
          category: 'core'
        },
        {
          id: 'clr',
          name: 'Culturally and Linguistically Responsive Practice (CLR)',
          description: '',
          popoverDescription: 'Access actionable instructions that respect cultural backgrounds, treating student diversity as an asset and fostering an inclusive environment where every child feels valued and empowered to succeed.',
          enabled: true,
          available: true,
          clrOptions: ['0-TK', 'K-12']
        },
        {
          id: 'teaching_tips',
          name: 'Teaching Tips for Standards',
          description: 'Available for Birth to Grade 12',
          popoverDescription: 'Get actionable teaching tips for each standard, helping teachers effectively implement lessons and ensure alignment with educational goals.',
          enabled: true,
          available: true
        },
        {
          id: 'reading_passage',
          name: 'Reading Passage',
          description: 'Available for K to Grade 12',
          popoverDescription: 'Access high-quality Anchor and Supporting Texts to strengthen literacy skills and improve learning outcomes, with adaptations based on grade level or Lexile to meet diverse student needs.',
          enabled: true,
          available: true
        },
        {
          id: 'typical_behaviors',
          name: 'Typical Behaviors and Observation Tips',
          description: 'Available for Birth to TK children only',
          popoverDescription: 'Get actionable teaching tips for each standard, helping teachers effectively implement lessons and ensure alignment with educational goals.',
          enabled: true,
          available: true
        },
        {
          id: 'udl',
          name: 'Universal Design for Learning (UDL)',
          description: 'Available for Birth to Grade 12',
          popoverDescription: 'Leveraging UDL to create inclusive learning experiences that support students with IEPs/IFSPs and English Language Learners, ensuring every learner thrives.',
          enabled: true,
          available: true
        },
        {
          id: 'teacher_slides',
          name: 'Teacher Slides',
          description: 'Available for TK to Grade 12',
          popoverDescription: 'Streamline lesson preparation with clear, engaging visuals for effective teaching.',
          enabled: true,
          available: true
        },
        {
          id: 'home_activities',
          name: 'At-Home Activities',
          description: 'Currently available for Birth to Grade 2 children',
          popoverDescription: 'Provide home-based activities aligned with lesson goals, helping children reinforce what the\'ve learned while fostering parent-child interaction.',
          enabled: true,
          available: true
        },
        {
          id: 'eduprotocols',
          name: 'Eduprotocols-based Student Activities',
          description: 'Available for K to Grade 12',
          popoverDescription: 'Integrate each lesson with EduProtocols templates to boost teaching efficiency and foster collaborative, creative, and interactive learning experiences. <br><span style="color: #FF7F41;">* The system will automatically match the best templates for all regular activities (excluding station activities).</span>',
          enabled: true,
          available: true
        },
        {
          id: 'formative_assessment',
          name: 'Formative Assessment',
          description: 'Available for K to Grade 12',
          popoverDescription: 'Automatically generate standard-aligned formative assessments to help teachers track student progress and adjust instruction for improved learning outcomes.',
          enabled: true,
          available: true
        },
        {
          id: 'mixed_age',
          name: 'Mixed-age Differentiations',
          description: 'Currently available for PS/PK (3-4) children in mixed-age classrooms upon adaptation',
          popoverDescription: 'Automatically generates specific instructions for younger age groups based on your classroom demographics when adapting lesson plans, helping teachers manage mixed-age classrooms and meet personalized needs more effectively.',
          enabled: true,
          available: true
        }
      ]
    }
  },
  watch: {
    visible (val) {
      this.dialogVisible = val
      if (val) {
        this.openDialog()
      }
    },
    // 监听模块列表变化，更新全选状态
    internalModuleList: {
      handler () {
        this.updateSelectAllState()
      },
      deep: true
    }
  },
  methods: {
    // 打开弹窗
    async openDialog () {
      this.dialogVisible = true
      await this.getModuleSettings()
      // 发送埋点事件
      if (this.setupEventName) {
        this.$analytics.sendEvent(this.setupEventName)
      }
    },

    // 获取模块设置
    async getModuleSettings () {
      try {
        this.loading = true
        await this.$store.dispatch('getLessonModuleSettings')
        // 初始化内部模块列表
        this.setLessonModuleData(this.lessonModuleSetting)
      } catch (error) {
        this.$message.error('Failed to load module settings')
      } finally {
        this.loading = false
      }
    },

    // 设置课程模块数据
    setLessonModuleData (settings) {
      if (!settings) return
      this.internalModuleList.forEach(module => {
        switch (module.id) {
          case 'teaching_tips':
            module.enabled = settings.teachingTips || false
            break
          case 'typical_behaviors':
            module.enabled = settings.typicalBehaviors || false
            break
          case 'teacher_slides':
            module.enabled = settings.teacherSlides || false
            break
          case 'eduprotocols':
            module.enabled = settings.eduprotocols || false
            break
          case 'mixed_age':
            module.enabled = settings.mixedAgeDifferentiations || false
            break
          case 'reading_passage':
            module.enabled = settings.readingPassage || false
            break
          case 'udl':
            module.enabled = settings.udl || false
            break
          case 'home_activities':
            module.enabled = settings.atHomeActivities || false
            break
          case 'formative_assessment':
            module.enabled = settings.formativeAssessment || false
            break
          case 'clr':
            // CLR 模块特殊处理
            const clrOptions = []
            if (settings.clr0toTk) {
              clrOptions.push('0-TK')
            }
            if (settings.clrKto12) {
              clrOptions.push('K-12')
            }
            module.clrOptions = clrOptions
            module.enabled = clrOptions.length > 0
            break
        }
      })
    },

    // 处理全选变化
    handleSelectAllChange (value) {
      this.internalModuleList.forEach(module => {
        if (module.available && module.id !== 'lesson_plan') {
          module.enabled = value
        }
        if (module.id === 'clr') {
          module.clrOptions = value ? ['0-TK', 'K-12'] : []
        }
      })
    },

    // 处理单个模块变化
    handleModuleChange (module) {
      // 如果是 CLR 模块，根据子选项状态更新模块状态
      if (module.id === 'clr') {
        // 当手动点击 CLR 模块时，如果当前是半选状态，则全选所有子选项
        if (module.enabled) {
          // 如果启用，确保至少有一个子选项          
          module.clrOptions = ['0-TK', 'K-12']
        } else {
          // 如果禁用，清空所有子选项
          module.clrOptions = []
        }
      }
    },

    // 处理 CLR 选项变化
    handleCLROptionsChange (value) {
      const clrModule = this.internalModuleList.find(module => module.id === 'clr')
      if (clrModule) {
        clrModule.clrOptions = value
        // 如果 CLR 选项被选中，则启用 CLR 模块
        clrModule.enabled = value.length > 0
        // 手动触发全选状态更新，确保状态正确
        this.$nextTick(() => {
          this.updateSelectAllState()
        })
      }
    },

    // 判断 CLR 模块是否为半选状态
    isCLRIndeterminate (module) {
      if (module.id !== 'clr') return false
      const clrOptions = module.clrOptions || []
      return clrOptions.length > 0 && clrOptions.length < 2
    },

    // 更新全选状态
    updateSelectAllState () {
      const availableModules = this.internalModuleList.filter(module => module.available && module.id !== 'lesson_plan')
      
      // 计算有效启用的模块数量（考虑 CLR 模块的特殊情况）
      let effectiveEnabledCount = 0
      let hasPartialSelection = false
      
      availableModules.forEach(module => {
        if (module.id === 'clr') {
          // CLR 模块特殊处理：根据子选项数量判断状态
          const clrOptions = module.clrOptions || []
          if (clrOptions.length === 2) {
            // 两个子选项都选中，视为完全启用
            effectiveEnabledCount++
          } else if (clrOptions.length === 1) {
            // 只有一个子选项选中，视为部分选中
            hasPartialSelection = true
          }
          // 没有子选项选中时，不增加计数
        } else {
          // 其他模块正常处理
          if (module.enabled) {
            effectiveEnabledCount++
          }
        }
      })
      
      if (availableModules.length === 0) {
        this.selectAll = false
        this.isIndeterminate = false
      } else if (effectiveEnabledCount === 0 && !hasPartialSelection) {
        // 没有任何模块启用且没有部分选中
        this.selectAll = false
        this.isIndeterminate = false
      } else if (effectiveEnabledCount === availableModules.length) {
        // 所有模块都完全启用
        this.selectAll = true
        this.isIndeterminate = false
      } else {
        // 有部分模块启用或有部分选中状态
        this.selectAll = false
        this.isIndeterminate = true
      }
    },

    // 获取启用的模块 ID 列表
    getNewSettings () {
      const result = {
        teachingTips: false,
        typicalBehaviors: false,
        teacherSlides: false,
        eduprotocols: false,
        mixedAgeDifferentiations: false,
        clr0toTk: false,
        clrKto12: false,
        readingPassage: false,
        udl: false,
        atHomeActivities: false,
        formativeAssessment: false
      }

      this.internalModuleList.forEach(module => {
        if (module.enabled) {
          switch (module.id) {
            case 'teaching_tips':
              result.teachingTips = true
              break
            case 'typical_behaviors':
              result.typicalBehaviors = true
              break
            case 'teacher_slides':
              result.teacherSlides = true
              break
            case 'eduprotocols':
              result.eduprotocols = true
              break
            case 'mixed_age':
              result.mixedAgeDifferentiations = true
              break
            case 'reading_passage':
              result.readingPassage = true
              break
            case 'udl':
              result.udl = true
              break
            case 'home_activities':
              result.atHomeActivities = true
              break
            case 'formative_assessment':
              result.formativeAssessment = true
              break
            case 'clr':
              // CLR 模块特殊处理，根据子选项设置
              const clrOptions = module.clrOptions || []
              if (clrOptions.includes('0-TK')) {
                result.clr0toTk = true
              }
              if (clrOptions.includes('K-12')) {
                result.clrKto12 = true
              }
              break
          }
        }
      })

      return result
    },

    // 显示模块信息
    showModuleInfo (module) {
      this.currentModuleInfo = module.description
      this.moduleInfoVisible = true
      setTimeout(() => {
        this.moduleInfoVisible = false
      }, 3000)
    },

    // 保存设置
    async handleSave () {
      try {
        this.saveLoading = true
        const settings = this.getNewSettings()
        await this.$store.dispatch('saveLessonModuleSettings', settings)
        this.dialogVisible = false
        this.$message.success(this.$t('loc.saveSfy'))
      } catch (error) {
        this.$message.error('Failed to save module settings')
      } finally {
        this.saveLoading = false
      }
    },

    // 取消操作
    handleCancel () {
      this.dialogVisible = false
    },

    // 处理卡片点击事件
    handleCardClick (module) {
      // 如果模块不可用或是 lesson_plan，则不处理点击
      if (!module.available || module.id === 'lesson_plan') {
        return
      }
      
      // 切换模块的启用状态
      module.enabled = !module.enabled
      
      // 如果是 CLR 模块，需要特殊处理
      if (module.id === 'clr') {
        if (module.enabled) {
          // 如果启用，确保至少有一个子选项
          module.clrOptions = ['0-TK', 'K-12']
        } else {
          // 如果禁用，清空所有子选项
          module.clrOptions = []
        }
      }
    },

    // 关闭弹窗
    handleClose () {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.lesson-module-settings {
  display: inline-block;
}

/* 弹窗样式 */
/deep/.lesson-module-dialog {
  border-radius: 8px;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    padding: 24px;
    padding-bottom: 16px;
    border-bottom: none;

    .el-dialog__title {
      font-weight: 600;
      font-size: 20px;
      color: #111C1C;
    }
  }

  .el-dialog__body {
    padding: 0 24px 24px;
  }

  .el-dialog__footer {
    padding: 0 24px 24px;
    padding-top: 0;
  }
}

.module-description {
  font-weight: 400;
  font-size: 16px;
  line-height: 1.5;
  color: #111C1C;
  margin-bottom: 20px;
}

.module-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-height: 400px;
  overflow-y: auto;

  .module-card {
    background-color: #F0F6FF;;
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
    min-height: 100px; // 确保卡片有足够的最小高度
    width: 400px;
    cursor: pointer; // 添加指针样式，表明可点击
    border: 1px solid transparent;

    &:hover {
      border-color: #10B3B7;
    }

    &.disabled-module {
      cursor: not-allowed; // 禁用状态显示不可点击  
    }

    &.disabled-module:hover {
      border-color: transparent !important;
    }

    &.skeleton {
      min-height: 120px;
    }

    .module-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 8px;
      gap: 8px;

      .module-checkbox {
        display: flex;
        flex: 1;
        min-width: 0; // 允许 flex 项目收缩
        
        .module-name {
          font-weight: 600;
          font-size: 14px;
          color: #111C1C;
          line-height: 1.4;
          word-wrap: break-word;
          white-space: normal;
          display: block;
          hyphens: auto; // 自动连字符
        }
      }

      .module-info-icon {
        color: #10B3B7;
        cursor: pointer;
        font-size: 16px;
        flex-shrink: 0;
        margin-top: 2px; // 稍微向下调整，与文本对齐
      }
    }

    .module-description-text {
      color: #606266;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .clr-options {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;

      /deep/ .el-checkbox-group {
        display: flex;
        gap: 16px;

        .el-checkbox {
          margin-right: 0;
          
          .el-checkbox__label {
            color: #606266;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .cancel-button {
    border: 1px solid #DCDFE6;
    color: #111C1C;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    height: 36px;
    background-color: #fff;
  }

  .save-button {
    background-color: #10B3B7;
    border: 1px solid #10B3B7;
    color: #FFFFFF;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
    height: 36px;
  }
}

/* 模块信息提示样式 */
/deep/.module-info-tooltip {
  max-width: 300px;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .module-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
<style lang="scss">
.module-info-popover {
  color: #111C1C;
}
</style>