<template>
  <div :class="{'hide-subscript': !showSource , 'has-placeholder': canEdit && isEmpty, 'not-can-edit': !canEdit}"
       :style="clrEditStyle"
       v-html="innerText"
       :contenteditable="canEdit"
       class="clr-content"
       :placeholder="placeholderText"
       @focus="isLocked = true"
       @blur="changeContent"
       @input="changeText"
       @click="toRefView"></div>
</template>

<script>
export default {
  name: 'ClrEditComponent',
  props: {
    canEdit: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    showSource: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: Number,
      default: 100
    },
    placeholder: {
      type: String,
      default: ''
    },
    generatedLessonSources: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isLocked: false, // 是否锁定不可用
      innerText: this.value
    }
  },
  computed: {
    clrEditStyle () {
      const style = {}
      if (this.height) {
        style.minHeight = `${this.height}px`
      }
      if (!this.canEdit) {
        style.border = 'none'
      }
      return style
    },
    placeholderText () {
      return this.placeholder || this.$t('loc.clrEditComponentPlaceholder')
    },
    isEmpty() {
      return !this.value || this.value === '' || this.value === '<br>';
    }
  },
  watch: {
    value () {
      if (this.loading) {
        this.innerText = this.value
      } else {
        if ((!this.isLocked && !this.innerText) || this.generatedLessonSources) {
          this.saveCursorPosition()
          this.innerText = this.value
          this.$nextTick(this.restoreCursorPosition)
          this.$emit('updateGeneratedLessonSources')
        }
      }
    }
  },
  methods: {
    changeContent (event) {
      this.isLocked = false
      const content = event.target.innerHTML
      this.$emit('input', content)
    },
    changeText (event) {
      const content = event.target.innerHTML
      this.$emit('input', content)
    },
    toRefView (event) {
      this.$emit('scrollView', event)
    },
    saveCursorPosition () {
      const selection = window.getSelection()
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        this.cursorPosition = {
          startContainer: range.startContainer,
          startOffset: range.startOffset,
          endContainer: range.endContainer,
          endOffset: range.endOffset
        }
      }
    },
    restoreCursorPosition () {
      if (this.cursorPosition) {
        const range = document.createRange()
        range.setStart(this.cursorPosition.startContainer, this.cursorPosition.startOffset)
        range.setEnd(this.cursorPosition.endContainer, this.cursorPosition.endOffset)
        const selection = window.getSelection()
        selection.removeAllRanges()
        selection.addRange(range)
      }
    }
  }
}
</script>

<style scoped lang="less">

.hide-subscript {
  border-bottom: 1px solid #ccc!important;
  border-radius: 8px;
  /deep/ a {
    display: none!important;
  }
}
.not-can-edit {
  border-bottom: none!important;
}
// .hide-subscript:empty::before {
//   color: #676879;
//   content: attr(placeholder);
//   font-size: 14px;
// }
.clr-content {
  position: relative;
}

/* 使用 ::before 模拟 placeholder */
.clr-content.has-placeholder::before {
  content: attr(placeholder);
  color: #676879;
  font-size: 14px;
  position: absolute;
  pointer-events: none;
  user-select: none;
}
</style>
