<template>
  <div>
    <!-- 单元 -->
    <div class="lg-padding-l-r-16 lg-padding-t-b-12 overflow-ellipsis">
      <img src="~@/assets/img/lesson2/unitPlanner/curriculum_unit.svg" style="width: 24px;height: 24px" alt=""
        height="32">
      <span class="lg-padding-left-12 font-size-16 font-weight-600" :title="unitInfo.title">{{ unitInfo.title }}</span>
      <!-- <div class="active-menu-mark" v-show="!activeLesson && !activeWeeklyPlan"></div> -->
    </div>
    <!-- 周计划 -->
    <el-collapse v-model="activeName" accordion v-loading="initUnitInfoLoading">
      <el-collapse-item v-for="wp in weeklyPlans" :key="wp.id" :name="wp.week"
        :class="{ 'active-week': isActiveWeek(wp) }">
        <template slot="title">
          <div>
            <div class="font-bold">{{ $t('loc.unitPlannerStep2WeekX', { week: wp.week }) }}</div>
            <div style="width: 240px;" class="overflow-ellipsis" :titke="wp.theme">{{ wp.theme }}</div>
          </div>
        </template>
        <div>
          <!-- 课程 -->
          <div class="menu-item sub-menu-item display-flex align-items justify-content-between"
            :class="{ 'active-menu': isCurrent(item) }" v-for="item in wp.items" :key="item.id"
            @click="selectLesson(item, true)">
            <div class="flex-auto">
              <!-- 周几 -->
              <div style="font-weight: 600">
                <span v-if="item.activitySortNum">{{ $t('loc.lesson') }} {{ item.activitySortNum }}</span>
                <span v-else>{{ item.day }}</span>
                <el-tag v-if="item.adaptedLesson && item.lessonId" class="adapted-tag-content font-weight-400"
                  size="mini">{{
                    $t('loc.adaptUnitPlanner25') }}</el-tag>
              </div>
              <!-- 课程名称 -->
              <div class="overflow-ellipsis">
                {{ item.title }}
              </div>
            </div>
            <!-- 确认状态 -->
            <div class="lesson-confirm-status">
              <!-- 已确认 -->
              <span class="text-success" v-if="lessonGenerated(item)">
                <i class="lg-icon lg-icon-approve"></i>
              </span>
              <span v-else-if="pendingAndProcessingItems(item)">
                <i class="el-icon el-icon-loading"></i>
              </span>
              <span v-else-if="failItems(item)">
                <i class="lg-icon lg-icon-reject color-text-danger"></i>
              </span>
            </div>
            <div class="active-menu-mark" v-show="isCurrent(item)"></div>
          </div>
          <div class="menu-item sub-menu-item display-flex align-items justify-content-between"
            :class="{ 'active-menu': isCurrent(item) }" v-for="(item, index) in wp.centerItems" :key="index"
            @click="selectLesson(item, true)">
            <div class="flex-auto">
              <!-- 周几 -->
              <div style="font-weight: 600">
                <span>{{ kToGrade2 ? $t('loc.unitPlannerStep3Stations') : $t('loc.unitPlannerStep3Centers') }} - {{
                  item.centerGroupName }}</span>
              </div>
              <!-- 课程名称 -->
              <div class="overflow-ellipsis">
                {{ item.title }}
              </div>
            </div>
            <!-- 确认状态 -->
            <div class="lesson-confirm-status">
              <!-- 已确认 -->
              <span class="text-success" v-if="lessonGenerated(item)">
                <i class="lg-icon lg-icon-approve"></i>
              </span>
              <span v-else-if="pendingAndProcessingItems(item)">
                <i class="el-icon el-icon-loading"></i>
              </span>
              <span v-else-if="failItems(item)">
                <i class="lg-icon lg-icon-reject color-text-danger"></i>
              </span>
            </div>
            <div class="active-menu-mark" v-show="isCurrent(item)"></div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
    <!-- 确认并生成课程弹窗 -->
    <el-dialog custom-class="confirm-generated-dialog" :close-on-click-modal="false" title="Confirmation"
      :append-to-body="true" :visible.sync="editLessonVisible" width="600px">
      <el-form ref="editLessonFormRef" label-position="top" label-width="100px" :model="editLesson" :rules="lessonRules"
        class="m-t-sm" v-if="editable">
        <!-- 课程标题 -->
        <el-form-item label="Lesson Title" prop="title">
          <el-input v-model="editLesson.title" maxlength="200"></el-input>
        </el-form-item>
        <!-- 课程类型 -->
        <el-form-item v-if="!editLesson.centerGroupName && showSmallAndLargeGroupFlag" label="Activity Type"
          prop="activityType">
          <el-select v-model="editLesson.activityType" class="w-full">
            <el-option :label="'Small Group'" :value="'Small Group'"></el-option>
            <el-option :label="'Large Group'" :value="'Large Group'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editLesson.centerGroupName && showSmallAndLargeGroupFlag" label="Center Name"
          prop="centerGroupName">
          <el-input :readonly="true" v-model="editLesson.centerGroupName"></el-input>
        </el-form-item>
        <!-- 测评点 -->
        <el-form-item label="Measures" prop="measures">
          <el-select v-model="editLesson.measures" filterable multiple class="w-full update-lesson-measure-select"
            :popper-class="'custom-popper-class'" :popper-append-to-body="false">
            <el-option-group v-for="domain in domains" :key="domain.id"
              :label="domain.abbreviation ? domain.abbreviation + ': ' + domain.name : domain.name">
              <!-- <span>{{ domain.abbreviation }}: {{ domain.name }}</span> -->
              <el-option v-for="measure in domain.children" :key="measure.id" :label="measure.abbreviation"
                :value="measure.abbreviation">
                <span v-if="measure.abbreviation !== measure.name && measure.abbreviation && measure.name">{{
                  measure.abbreviation }}: {{ measure.name }}</span>
                <span v-else-if="measure.abbreviation == measure.name">{{ measure.name }}</span>
                <span v-else>{{ measure.abbreviation }}: {{ measure.description }}</span>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <!-- 校训 -->
        <el-form-item v-if="!editLesson.centerId && rubricsOptions && rubricsOptions.length > 0"
          :label="$t('loc.unitPlannerStep2Rubrics')" prop="rubrics">
          <el-select v-model="editLesson.rubrics" filterable multiple class="w-full update-lesson-rubrics-select"
            :popper-append-to-body="false" :placeholder="$t('loc.pleaseSelect')">
            <el-option v-for="(rubric, index) in rubricsOptions" :key="index" :label="rubric.title"
              :value="rubric.title">
              <div class="rubric-option">
                <div class="rubric-title">{{ rubric.title }}</div>
                <div class="rubric-description">{{ rubric.description }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 描述 -->
        <el-form-item label="Description" prop="description">
          <el-input type="textarea" v-model="editLesson.description" maxlength="1000"
            :autosize="{ minRows: 2, maxRows: 6 }"></el-input>
        </el-form-item>
      </el-form>
      <!-- 操作 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="editLessonVisible = false" :disabled="updateLessonLoading">Cancel</el-button>
        <el-button type="primary" @click="updateItemAndGenerateLesson" :loading="updateLessonLoading">Confirm &
          Generate</el-button>
      </span>
    </el-dialog>
    <!-- 批量生成课程确认弹窗 -->
    <el-dialog title="Batch Generate Detailed Lesson Plans" custom-class="batch-generated-dialog"
      :close-on-click-modal="false" :append-to-body="true" :before-close="handleClose"
      :visible.sync="batchGenerateVisible" width="30%">
      <span>
        <div class="font-size-16">Would you like to enable batch generation for a quick creation of lesson plans for all
          weeks?</div>
        <div class="font-size-16">You can also click the <img
            src="~@/assets/img/lesson2/unitPlanner/unit_planner_batch_generate.svg" alt=""> button in the upper-right
          corner
          of this page to initiate this process.</div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">Later</el-button>
        <el-button type="primary" @click="batchGenerateUnitLessons(true)">Confirm & Generate</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { UnitPlanCenterIconMap } from '@/utils/constants'
import { mapState } from 'vuex'
import Lessons2 from '@/api/lessons2'
import { equalsIgnoreCase } from '@/utils/common'
export default {
  props: {
    // 单元信息
    unitInfo: {
      type: Object,
      default: () => {
      }
    },
    // 周计划列表
    weeklyPlans: {
      type: Array,
      default: () => []
    },
    // 是否禁用切换
    disableSwitch: {
      type: Boolean,
      default: false
    },
    // 是否是 Center 课程
    isCenterLesson: {
      type: Boolean,
      default: false
    },
    isNext: {
      type: Boolean,
      default: false
    },
    // 当前课程是否是在生成中
    currentGenerate: {
      type: Boolean,
      default: false
    },
    batchGenerating: {
      type: Boolean,
      default: false
    },
    completeAllLesson: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 标题校验，不可为空
    let titleValidator = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error('Please input title'))
      } else {
        callback()
      }
    }
    // 描述校验，不可为空
    let descriptionValidator = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error('Please input description'))
      } else {
        callback()
      }
    }
    return {
      activeName: 1, // 当前选中的周计划
      iconMap: UnitPlanCenterIconMap, // Center 课程图标
      activeItem: null, // 当前选中的活动项
      activeLesson: null, // 当前选中的课程
      activeWeeklyPlan: null, // 当前选中的周计划
      hasNextUnconfirmedLesson: false, // 是否有下一个未确认的课程
      allConfirmed: false, // 是否所有课程都已确认
      unconfirmedLessonOverviewWeeklyPlan: null, // 下一个未确认课程概览的周计划
      initUnitInfoLoading: false, // 初始化单元信息加载状态
      editLesson: null, // 未确认的周计划项
      unconfirmedItem: null, // 未确认的周计划项
      editLessonVisible: false, // 编辑课程弹窗
      updateLessonLoading: false, // 更新课程加载状态
      domains: [], // 测评点
      lessonRules: {
        title: [
          { required: true, message: 'Please input title', trigger: 'blur' },
          { required: true, message: 'Please input title', trigger: 'change' },
          { validator: titleValidator, trigger: 'blur' }
        ],
        measures: [
          { required: true, message: 'Please input unit measures', trigger: 'blur' },
          { required: true, message: 'Please input unit measures', trigger: 'change' }
        ],
        description: [
          { required: true, message: 'Please input description', trigger: 'blur' },
          { required: true, message: 'Please input description', trigger: 'change' },
          { validator: descriptionValidator, trigger: 'blur' }
        ]
      },
      editable: false, // 是否可编辑
      progressConstant: {
        sixtyPercent: 'WEEK_OVERVIEW_CONFIRMED',
        eightyPercent: 'LESSON_OVERVIEW_GENERATED'
      }, // 进度常量
      batchGenerateVisible: false, // 批量生成课程
      currentLessonItem: null, // 当前课程
      isShowHomeActivity: false // 是否显示家庭活动
    }
  },
  mounted() {
    // 需要根据年龄组选择是否需要调用家庭活动
    this.setShowHomeActivity()
    this.$bus.$on('getAdaptedUnitWeeklyPlans', () => {
      this.updateWeeklyPlans()
    })
  },
  beforeDestroy () {
    this.$bus.$off('getAdaptedUnitWeeklyPlans')
  },
  watch: {
    // 监听批量生成课程提示弹窗
    batchGenerateVisible(val) {
      if (val) {
        this.$analytics.sendEvent('web_unit_create3_det_batch_pop')
      }
    },
    // 监听激活的周计划
    activeItem(val) {
      if (val) {
        this.updatePromptSourceList(val)
      }
    }
  },

  async created() {
    // 初始化当前课程
    await this.initCurrentLesson()
  },

  computed: {
    ...mapState({
      plans: state => state.curriculum.unit.weeklyPlans,
      currentUser: state => state.user.currentUser, // 当前用户
      unit: state => state.curriculum.unit, // 单元信息
      baseInfo: state => state.curriculum.unit.baseInfo, // 单元基本信息
      batchId: state => state.unit.batchId, // 批量生成 ID
      isCurriculumPlugin: state => state.curriculum.isCurriculumPlugin, // 是否是 Curriculum Plugin 平台
      batchTasks: state => state.unit.batchTasks, // 批量生成任务
      showSmallAndLargeGroupFlag: state => state.curriculum.showSmallAndLargeGroupFlag, // 是否显示 Small Group 和 Large Group
    }),
    // 年龄组是否是 K-Grade 2
    kToGrade2() {
      return ['K (5-6)', 'Grade 1', 'Grade 2'].includes(this.baseInfo.grade)
    },
    // 是否是 K-12 年级
    isK12Grade() {
      let k12Grades = ['K (5-6)', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Mixed age group']
      return this.baseInfo.grade && k12Grades.indexOf(this.baseInfo.grade) > -1
    },
    // 是否是当前选中的
    isCurrent() {
      return function (obj) {
        if (obj && obj.id) {
          return (this.activeItem && this.activeItem.id.toUpperCase() === obj.id.toUpperCase()) || (this.activeWeeklyPlan && this.activeWeeklyPlan.id.toUpperCase() === obj.id.toUpperCase())
        } else {
          return false
        }
      }
    },
    // 当前选中课程是否再本周
    isActiveWeek() {
      return function (obj) {
        let items = this.isCenterLesson ? obj.centerItems : obj.items
        return this.activeName != obj.week && items.some(item => this.isCurrent(item))
      }
    },
    // 课程是否已生成完毕
    lessonGenerated() {
      return function (item) {
        if (item.lesson) {
          if (item.lesson.confirmed) {
            return true
          }

          if (item.centerId) {
            return (
              !!item.lesson.objectives &&
              !!item.lesson.materials &&
              !!item.lesson.implementationSteps &&
              !item.generateLessonDetailLoading
            ) || this.completedItems(item)
          }

          let loading = false
          if (this.isCurrent(item) && this.currentGenerate) {
            loading = true
          }

          // 判断模块开关
          const teacherGuideOn = this.getAdaptedModuleSwitch('teacherGuideFlag')
          const standardsAssessmentOn = this.getAdaptedModuleSwitch('standardsAssessmentFlag')

          // 高年级不判断家庭活动
          if (!this.isShowHomeActivity) {
            return (
              !!item.lesson.objectives &&
              !!item.lesson.materials &&
              !!item.lesson.implementationSteps &&
              (!teacherGuideOn || item.lesson.universalDesignForLearning || item.lesson.universalDesignForLearningGroup) &&
              (!teacherGuideOn || item.lesson.culturallyResponsiveInstruction || item.lesson.culturallyResponsiveInstructionGroup) &&
              !loading &&
              (
                (!this.isK12Grade && (!teacherGuideOn || (item.lesson.typicalBehaviors && item.lesson.typicalBehaviors.length > 0))) ||
                (this.isK12Grade && (!standardsAssessmentOn || (item.lesson.questions && item.lesson.questions.length > 0)))
              )
            ) || this.completedItems(item)
          }

          // 需要判断家庭活动
          return (
            !!item.lesson.objectives &&
            !!item.lesson.materials &&
            !!item.lesson.implementationSteps &&
            (!teacherGuideOn || item.lesson.universalDesignForLearning || item.lesson.universalDesignForLearningGroup) &&
            (!teacherGuideOn || item.lesson.culturallyResponsiveInstruction || item.lesson.culturallyResponsiveInstructionGroup) &&
            !loading &&
            (
              (!this.isK12Grade && (!teacherGuideOn || (item.lesson.typicalBehaviors && item.lesson.typicalBehaviors.length > 0))) ||
              (this.isK12Grade && (!standardsAssessmentOn || (item.lesson.questions && item.lesson.questions.length > 0)))
            ) &&
            !!item.lesson.homeActivity &&
            !item.lesson.generateHomeActivityLoading
          ) || this.completedItems(item)
        } else {
          return !!item.lessonId || this.completedItems(item)
        }
      }
    },
    completedItems() {
      return function (item) {
        if (!this.batchTasks) {
          return false
        }
        const completed = this.batchTasks.completedTasks
        if (completed && completed.length > 0) {
          return completed.some(task => equalsIgnoreCase(task.itemId, item.id)) && !item.processing
        }
        return false
      }
    },
    // 是否有正在处理或待处理的活动项
    pendingAndProcessingItems() {
      return function (item) {
        if (!this.batchTasks) {
          let loading = false
          // 如果当前课程是活动项并且正在生成课程，则显示加载中
          if (this.isCurrent(item) && this.currentGenerate) {
            loading = true
          }
          return item.processing || loading
        }
        const pendingAndProcessing = this.batchTasks.processingTasks.concat(this.batchTasks.pendingTasks)
        if (pendingAndProcessing && pendingAndProcessing.length > 0) {
          return pendingAndProcessing.some(task => equalsIgnoreCase(task.itemId, item.id)) || item.processing
        }
        return false
      }
    },
    // 是否有失败的活动项
    failItems() {
      return function (item) {
        if (!this.batchTasks) {
          return item.error
        }
        const failed = this.batchTasks.failedTasks
        if (failed && failed.length > 0) {
          return (failed.some(task => task.itemId === item.id) && !item.processing) || item.error
        }
        return false
      }
    },
    // 单元可选的校训选项
    rubricsOptions() {
      return this.baseInfo && this.baseInfo.newRubrics || []
    }
  },
  methods: {
    // 获取轻度改编模块开关, 返回 true 表示开启
    getAdaptedModuleSwitch(model) {
      if (!this.unit || !this.unit.adaptedModuleSwitch) {
        return true
      }
      return this.unit.adaptedModuleSwitch[model]
    },
    // 更新 promptSourceList
    updatePromptSourceList(val) {
      if (val) {
        let measures = val.measures
        if (!measures || measures.length === 0) {
          measures = val.lesson.measures
        }
        // 定义一个资源数组
        const sources = [
          { sourceStep: 4, sourceName: 'lesson title', sourceValue: val.title },
          { sourceStep: 4, sourceName: 'activity type', sourceValue: val.activityType },
          { sourceStep: 4, sourceName: 'number of measures', sourceValue: measures.length },
          { sourceStep: 4, sourceName: 'day N', sourceValue: val.dayOfWeek },
          { sourceStep: 4, sourceName: 'specific standard names', sourceValue: measures.join(', ') },
          { sourceStep: 4, sourceName: 'activity description', sourceValue: val.description }
        ]
        // 更新数据
        this.$store.dispatch('curriculum/updatePromptSourcesList', {
          step: 3,
          sources: sources
        })
      }
    },
    // 初始化当前课程
    async initCurrentLesson() {
      // 没有数据则跳过
      if ((!this.weeklyPlans || this.weeklyPlans.length === 0) || (this.weeklyPlans && (this.weeklyPlans[0].items.length === 0 && this.weeklyPlans[0].centerItems.length === 0))) {
        await this.initUnitInfo()
        await this.initWeeklyPlanData()
        return
      }
      // 下一个待确认的课程
      let nextUnconfirmedLessonItem = this.getNextLesson(this.$route.params.itemId)
      let nextUnconfirmedLesson = nextUnconfirmedLessonItem ? nextUnconfirmedLessonItem.lesson : null
      if (nextUnconfirmedLessonItem) {
        this.activeName = this.currentLessonWeek(nextUnconfirmedLessonItem)
        // 更新当前课程
        this.changeLesson(nextUnconfirmedLessonItem, nextUnconfirmedLesson)
      }
    },
    // 判断是否显示家庭活动
    setShowHomeActivity() {
      var grade = this.baseInfo.grade
      // 需要调用家庭活动的年级
      let showHomeActivity = ['Infant (0-1)', 'Young Toddler (1-2)', 'Toddler (2-3)', 'PS/PK (3-4)', 'TK (4-5)', 'K (5-6)', 'Grade 1', 'Grade 2']
      // 判断是否显示 Small Group 和 Large Group
      this.isShowHomeActivity = showHomeActivity.includes(grade)
    },

    // 初始化单元信息
    async initUnitInfo() {
      return new Promise((resolve, reject) => {
        let params = {
          params: {
            unitId: this.$route.params.unitId,
            edit: true
          }
        }
        this.$axios.get($api.urls().getUnitInfo, params).then(res => {
          if (res.lockedData) {
            let userName = res.lockedData.userName
            this.$message.error(userName + 'is editing this unit.')
            this.$router.push({
              name: this.$store.state.curriculum.isCG || this.isCurriculumPlugin ? 'unit-planner-cg' : 'unitPlanner'
            })
          }
          let unit = res.unit
          // 单元基本信息
          unit.baseInfo = {
            title: unit.title, // 标题
            number: unit.number, // 序号
            country: unit.country || 'United States', // 国家
            state: unit.state, // 状态
            city: unit.city, // 城市
            description: unit.description, // 描述
            frameworkId: unit.frameworkId, // 框架 ID
            frameworkName: unit.frameworkName, // 框架名称
            domainIds: unit.domainIds, // 领域 ID
            allDomains: unit.options, // 选项
            grade: unit.grade, // 年级
            language: unit.language, // 语言
            weekCount: unit.weekCount, // 周数
            progress: unit.progress, // 进度
            rubrics: unit.rubrics, // 校训
            newRubrics: unit.newRubrics, // 新校训
            useLocation: unit.useLocation, // 是否显示国家城市
            useDomain: unit.useDomain, // 是否按领域选择
            measureIds: unit.measureIds, // measure ID
            useLessonTemplate: unit.useLessonTemplate, // 是否使用课程模板
            showLessonTemplateTip: unit.showLessonTemplateTip, // 是否显示课程模板提示
            classroomType: unit.classroomType || 'IN_PERSON', // 课堂类型：IN_PERSON 或 VIRTUAL
            anchorText: unit.anchorText, // Anchor Text
            readingLevelSetting: unit.readingLevelSetting, // 阅读能力设置
            adaptedType: unit.adaptedType, // 单元导入改编类型
            adaptedModuleSwitch: unit.adaptedModuleSwitch, // 改编模块开关
            measures: unit.measures.map(measure => {
              return {
                id: measure.id,
                abbreviation: measure.abbreviation,
                name: measure.name
              };
            }) // measures 已选测评点
          }
          unit.adaptedType = res.unit.adaptedType // 单元导入改编类型
          unit.adaptedModuleSwitch = res.unit.adaptedModuleSwitch
          // 单元概览信息
          unit.overview = {
            title: unit.title, // 标题
            coverMedias: unit.coverMedias || [],
            coverKeywords: unit.coverKeywords, // 封面搜索关键词
            overview: unit.overview, // 概览
            concepts: unit.concepts, // 概念
            guidingQuestions: unit.guidingQuestions, // 指导问题
            trajectory: unit.trajectory // 轨迹
          }
          this.$store.commit('curriculum/SET_UNIT', unit)
          // 设置扩展参数
          this.$store.dispatch('curriculum/setPromptExtensionParams', {
            unitId: unit.id
          })
          resolve()
        })
      })
    },
    updateWeeklyPlans() {
      let params = {
        params: {
          unitId: this.$route.params.unitId
        }
      }
      this.$axios.get($api.urls().getPlanThemeAndOverview, params).then(async res => {
        if (res.weeklyPlans && res.weeklyPlans.length > 0) {
          let weeklyPlans = res.weeklyPlans
          for (let i = 0; i < this.weeklyPlans.length; i++) {
            let weeklyPlan = weeklyPlans[i]
            let weeklyPlanId = weeklyPlan.id
            let plan = this.weeklyPlans.find(plan => equalsIgnoreCase(plan.id, weeklyPlanId))
            if (plan) {
              plan.overview = weeklyPlan.overview
              plan.theme = weeklyPlan.theme
            }
          }
        }
      })
    },
    // 初始化周计划数据
    async initWeeklyPlanData() {
      this.initUnitInfoLoading = true
      let params = {
        params: {
          unitId: this.$route.params.unitId
        }
      }
      // 调用接口获取周计划概览与主题数据
      await this.$axios.get($api.urls().getPlanThemeAndOverview, params).then(async res => {
        if (res.weeklyPlans && res.weeklyPlans.length > 0) {
          let weeklyPlans = res.weeklyPlans
          weeklyPlans.forEach(plan => {
            plan.rubrics = plan.newRubrics
          })
          this.$store.commit('curriculum/SET_WEEKLY_PLANS', weeklyPlans)
          // 保存缓存是否是旧数据
          this.$store.commit('curriculum/SET_IS_OLD_UNIT', this.checkOldUnitData(res.weeklyPlans))
          // 获取所有的周计划项
          this.getAllPlanItems()
        }
      })
    },
    // 是否是旧数据 （Weekly Theme 添加测评点之前）
    checkOldUnitData(weeklyPlans) {
      if (!weeklyPlans || weeklyPlans.length === 0) return false

      const firstPlan = weeklyPlans[0]
      const hasOverview = firstPlan.overview && firstPlan.overview.trim() !== ''
      const noMeasureIds = firstPlan.measureIds && !firstPlan.measureIds.length

      // 如果有 overview 但是没测评点数据判定为旧数据
      return hasOverview && noMeasureIds
    },
    // 获取所有的周计划项目
    async getAllPlanItems() {
      let promises = []
      if (this.weeklyPlans && this.weeklyPlans.length > 0) {
        this.weeklyPlans.forEach(weeklyPlan => {
          promises.push(this.getPlanItemsbyPlanId(weeklyPlan.id))
        })
      } else {
        this.plans.forEach(weeklyPlan => {
          promises.push(this.getPlanItemsbyPlanId(weeklyPlan.id))
        })
      }
      await Promise.all(promises)
        .then(async res => {
          res.forEach(weeklyPlan => {
            for (let i = 0; i < this.weeklyPlans.length; i++) {
              if (this.weeklyPlans[i].id.toLowerCase() === weeklyPlan.id.toLowerCase()) {
                this.$set(this.weeklyPlans[i], 'items', weeklyPlan.items)
                this.$set(this.weeklyPlans[i], 'centerItems', weeklyPlan.centerItems)
              }
            }
          })
          // 初始化周计划进度
          if (!this.batchId) {
            this.initWeeklyPlansProgressing()
          }
          this.initUnitInfoLoading = false
          // 初始化课程信息
          await this.initCurrentLesson()
        })
    },
    initWeeklyPlansProgressing() {
      this.weeklyPlans.forEach(weeklyPlan => {
        weeklyPlan.items.forEach(item => {
          this.$set(item, 'processing', false)
        })
        weeklyPlan.centerItems.forEach(item => {
          this.$set(item, 'processing', false)
        })
      })
    },
    // 通过周计划 ID 获取周计划项
    async getPlanItemsbyPlanId(planId) {
      return new Promise((resolve, reject) => {
        let params = {
          params: {
            planId: planId
          }
        }
        this.$axios.get($api.urls().getPlanActivities, params)
          .then(res => {
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 选择单元
    selectUnit() {
      if (this.disableSwitch) {
        return
      }
      // 更新当前课程
      this.changeLesson()
      // 检查下一个课程状态
      this.getNextLesson()
    },

    // 选择周计划
    selectWeek(weeklyPlan) {
      if (this.disableSwitch) {
        return
      }
      // 更新当前课程
      this.changeLesson(null, null, weeklyPlan)
      // 检查下一个课程状态
      this.getNextLesson()
    },

    currentLessonWeek(currentItem) {
      let weeklyPlan = this.weeklyPlans.find(x => {
        let items = [...x.centerItems, ...x.items]
        let item = items.find(item => item.id.toUpperCase() === currentItem.id.toUpperCase())
        if (item) {
          return x
        }
      })
      return (weeklyPlan && weeklyPlan.week) || 1
    },
    // 关闭批量生成课程提示弹窗
    handleClose(cancel = true) {
      // 隐藏引导
      this.hideGuide()
      if (cancel) {
        this.selectLesson(this.currentLessonItem)
      }
      // 如果点击了取消，则不再显示批量生成课程提示，继而继续单个生成操作
      if (this.isNext) {
        this.$emit('keepOnSingleLessonGenerate')
      }
    },
    // 隐藏引导
    hideGuide() {
      // 关闭弹窗
      this.batchGenerateVisible = false
      // 发起请求隐藏批量生成课程提示
      let result = { 'features': ['SHOW_BATCH_GENERATE_LESSON_TIP'] }
      this.$axios.post($api.urls().hideGuide, result).then()
      localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP', false)
    },
    // 选择课程
    selectLesson(item, isTip = false) {
      if (this.disableSwitch) {
        return
      }
      // 获取是否需要批量生成课程提示
      const showBatchGenerateLessonTip = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP'))
      // 判断是否需要批量生成课程提示
      if (isTip && showBatchGenerateLessonTip && !this.completeAllLesson) {
        this.batchGenerateVisible = true
        this.currentLessonItem = item
        return
      }
      this.batchGenerateVisible = false
      this.activeName = this.currentLessonWeek(item)
      let lesson = item.lesson
      console.log('item11111111111', item)
      // 当前课程是否确认,没有确认过的先确认
      if (!item.confirmed) {
        // 课程概览未确认，确认后生成
        this.unconfirmedItem = item
        this.editLesson = JSON.parse(JSON.stringify(this.unconfirmedItem))
        if (this.rubricsOptions && this.rubricsOptions.length > 0) {
          this.editLesson.rubrics = this.unconfirmedItem.rubrics.map(rubric => rubric.title)
        }
        // 获取路由参数 unitId
        const unitId = this.$route.params.unitId
        this.$store.dispatch('curriculum/getMeasuresContainTopAndBottom', { frameworkId: this.unit.baseInfo.frameworkId, unitId: unitId }).then((domains) => {
          this.domains = domains
          this.editable = true
          this.editLessonVisible = true
        })
        return
      }
      // 更新当前课程
      this.changeLesson(item, lesson)
      // 检查下一个课程
      this.getNextLesson()
    },

    // 获取下一个课程
    getNextLesson(itemId) {
      let weeklyPlans = this.weeklyPlans
      // 下一个课程
      let nextLessonItem = null
      // 遍历周计划列表
      if (this.activeItem && this.activeItem.planId) {
        let currentWeeklyPlan = this.weeklyPlans.find(x => x.id.toUpperCase() === this.activeItem.planId.toUpperCase())
        let items = this.isCenterLesson ? currentWeeklyPlan.centerItems : currentWeeklyPlan.items
        nextLessonItem = items.find(item => item.confirmed && !item.lessonId && item.id !== this.activeItem.id)
      }
      if (!nextLessonItem) {
        for (let i = 0; i < weeklyPlans.length; i++) {
          let weeklyPlan = weeklyPlans[i]
          // 遍历所有课程列表
          let items = [...weeklyPlan.centerItems, ...weeklyPlan.items]
          // 如果有指定的课程，找到指定的课程
          if (itemId) {
            nextLessonItem = items.find(item => item.id.toUpperCase() === itemId.toUpperCase())
          } else {
            for (let j = 0; j < items.length; j++) {
              let item = items[j]
              // let lesson = item.lesson
              // 是否是当前课程
              let isCurrent = this.activeItem && this.activeItem === item
              if (item.confirmed && !item.lessonId && !isCurrent) {
                nextLessonItem = item
                break
              }
            }
          }
          if (nextLessonItem) {
            break
          }
        }
      }
      // 没有下一个课程了
      this.hasNextUnconfirmedLesson = !!nextLessonItem || !!this.getNextUnconfirmedLesson()
      // 课程概览待确认的周
      let unconfirmedLessonOverviewWeeklyPlan = weeklyPlans.find(weeklyPlan => !this.weeklyLessonOverviewCompleted(weeklyPlan))
      if (unconfirmedLessonOverviewWeeklyPlan) {
        this.unconfirmedLessonOverviewWeeklyPlan = unconfirmedLessonOverviewWeeklyPlan
      } else {
        this.unconfirmedLessonOverviewWeeklyPlan = null
      }
      // 所有课程已经确认
      if (!this.unconfirmedLessonOverviewWeeklyPlan && !nextLessonItem) {
        this.allConfirmed = true
      } else {
        this.allConfirmed = false
      }
      // 下一个课程状态更新
      this.$emit('changeNextLessonStatus', this.hasNextUnconfirmedLesson, this.allConfirmed, this.unconfirmedLessonOverviewWeeklyPlan)
      return nextLessonItem
    },

    // 获取下一个未确认的课程
    getNextUnconfirmedLesson() {
      let nextLessonItem = null
      let weeklyPlans = this.weeklyPlans
      for (let i = 0; i < weeklyPlans.length; i++) {
        if (nextLessonItem) {
          break
        }
        let weeklyPlan = weeklyPlans[i]
        // 遍历课程列表
        let items = this.isCenterLesson ? weeklyPlan.centerItems : weeklyPlan.items
        for (let j = 0; j < items.length; j++) {
          let item = items[j]
          // 是否是当前课程
          let isCurrent = this.activeItem && this.activeItem === item
          if (!item.confirmed && !isCurrent) {
            nextLessonItem = item
            break
          }
        }
      }
      return nextLessonItem
    },

    // 更新并生成课程
    updateItemAndGenerateLesson() {
      this.$refs.editLessonFormRef.validate((valid) => {
        if (valid) {
          this.activeItem = this.unconfirmedItem
          this.$set(this.unconfirmedItem, 'confirmed', true)
          this.$set(this.unconfirmedItem, 'title', this.editLesson.title)
          this.$set(this.unconfirmedItem, 'description', this.editLesson.description)
          this.$set(this.unconfirmedItem, 'measures', this.editLesson.measures)
          this.$set(this.unconfirmedItem, 'activityType', this.editLesson.activityType)
          let rubrics = []
          if (this.rubricsOptions && this.rubricsOptions.length > 0) {
            // 更新校训
            rubrics = this.rubricsOptions.filter(rubric => this.editLesson.rubrics.includes(rubric.title))
            this.$set(this.unconfirmedItem, 'rubrics', rubrics)
          }
          let items = []
          items.push(this.unconfirmedItem)
          let params = {
            items: items,
            frameworkId: this.unit.baseInfo.frameworkId,
            planId: this.unconfirmedItem.planId,
            clearItemLesson: true, // 清除课程信息
            unitId: this.unit.id // 单元 ID
          }
          // 重新判断当前 Unit 的生成进度
          params.progress = this.reJudgeUnitGenerateProgress()
          this.updateLessonLoading = true
          this.$axios.post($api.urls().updatePlanItems, params).then((res) => {
            this.updateLessonLoading = false
            this.editLessonVisible = false
            this.editable = false
            this.changeLesson(this.unconfirmedItem, null)
            this.getNextLesson()
          }).catch(error => {
            this.updateLessonLoading = false
            this.$message.error(error.message)
          })
          // 更新周计划校训
          let currentWeeklyPlan = this.weeklyPlans.find(x => x.id.toUpperCase() === this.unconfirmedItem.planId.toUpperCase())
          let currentRubrics = currentWeeklyPlan.rubrics || []

          // 使用 Map 对校训进行去重,以 title 为 key
          const rubricsMap = new Map()
          // 先添加当前周计划的校训
          currentRubrics.forEach(rubric => {
            if (rubric.title) {
              rubricsMap.set(rubric.title, rubric)
            }
          })
          // 再添加新的校训
          rubrics.forEach(rubric => {
            if (rubric.title) {
              rubricsMap.set(rubric.title, rubric)
            }
          })

          // 将 Map 转换为数组
          const allRubrics = Array.from(rubricsMap.values())

          // 判断是否变化了,如果变化了,则更新周计划校训
          if (currentRubrics.length !== allRubrics.length ||
            !currentRubrics.every(currentRubric =>
              allRubrics.some(newRubric => newRubric.title === currentRubric.title))) {
            this.$set(currentWeeklyPlan, 'rubrics', allRubrics)
            const params = {
              planId: currentWeeklyPlan.id,
              rubrics: allRubrics
            }
            // 更新周计划校训
            Lessons2.updateWeeklyRubrics(params)
          }
        }
      })
    },

    // 重新判断当前 Unit 的生成进度
    reJudgeUnitGenerateProgress() {
      // 是否存在未确定的 Item
      let hasUnconfirmedItem = false
      // 是否存在未生成的 Item 的周次
      let hasUnGenerateItem = false
      // Unit 生成进度
      let progress = ''
      // 遍历所有周计划，如果有未确认的课程，则更新进度为 60%
      for (let i = 0; i < this.weeklyPlans.length; i++) {
        // 获取当前周计划
        let weeklyPlan = this.weeklyPlans[i]
        // 判断是否存在周次未生成大小组
        if (!weeklyPlan.items || weeklyPlan.items.length === 0) {
          hasUnGenerateItem = true
          break
        }
        // 如果周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.items && weeklyPlan.items.length > 0) {
          if (weeklyPlan.items.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
        // 如果 centers 组周计划项存在，则判断是否有未确认的课程
        if (weeklyPlan.centerItems && weeklyPlan.centerItems.length > 0) {
          if (weeklyPlan.centerItems.some(item => !item.confirmed)) {
            hasUnconfirmedItem = true
            break
          }
        }
      }
      // 如果存在未确认的课程或未生成大小组的周次，则更新进度为 60%，否则不进行更新操作
      if (!this.baseInfo.progress || this.baseInfo.progress < 60 || hasUnconfirmedItem || hasUnGenerateItem) {
        progress = this.progressConstant.sixtyPercent
      } else if (!hasUnconfirmedItem) { // 如果不存在未确定的 Item，则更新进度为 80%
        progress = this.progressConstant.eightyPercent
      }
      return progress
    },

    // 一周的课程概览是否都确认完成
    weeklyLessonOverviewCompleted(weeklyPlan) {
      return weeklyPlan && weeklyPlan.items && !weeklyPlan.items.some(item => !item.confirmed)
    },

    // 切换课程
    changeLesson(item, lesson, weeklyPlan) {
      // 如果禁用切换并且不是批量生成课程，则不允许切换
      if (this.disableSwitch && !this.batchGenerating) {
        return
      }
      this.activeItem = item
      this.activeLesson = lesson
      this.activeWeeklyPlan = weeklyPlan
      this.activeName = this.currentLessonWeek(item)
      // 向父组件传递事件
      this.$emit('changeLesson', item, lesson, weeklyPlan)
    },
    // 批量生成课程
    batchGenerateUnitLessons(notTip = false, isBatch = true, item) {
      // 点击批量重置重试次数
      this.$store.dispatch('unit/setResetMaxRetry', 0)
      if (this.batchGenerateVisible) {
        this.hideGuide()
      }
      // 点击批量时先为 Vuex 中的 appearNotice 设置为 false
      this.$store.dispatch('unit/setAppearNotice', false)
      // 点击批量生成时先将 Vuex 中的数据清除
      this.$store.dispatch('unit/setBatchTasks', null)
      if (this.currentLessonItem) {
        this.selectLesson(this.currentLessonItem)
      }
      const result = this.unGenerateLessons()
      const items = [...new Set(result.items)]
      const params = {
        unitId: this.unit.id,
        generateItems: items
      }
      let tips = this.$t('loc.lessonConfirmTip')
      // 如果不是提示弹窗，显示二次确认弹窗提示由用户自主选择是否生成，否则根据弹窗中用户是否点击确认按钮来判断是否生成
      if (!notTip) {
        this.$confirm(tips, 'Confirmation', {
          confirmButtonText: result.existUnConfirmed ? 'Confirm & Generate' : 'Confirm',
          cancelButtonText: 'Later',
          customClass: 'width-418-confirm-message-box'
        }).then(() => {
          const showBatchGenerateLessonTip = JSON.parse(localStorage.getItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP'))
          if (showBatchGenerateLessonTip) {
            this.handleClose(false)
          }
          this.unGenerateLessons(true)
          params.batch = isBatch
          Lessons2.createGenerateLessonTasks(params).then(res => {
            this.$store.dispatch('unit/setBatchId', res.id)
            this.$store.dispatch('unit/setAppearNotice', true)
          }).catch(() => {
            this.unGenerateLessons()
          })
        }).catch(() => {
        })
      } else {
        // 判断是否是批量生成
        if (isBatch) {
          params.batch = true
        } else {
          this.$analytics.sendEvent('web_unit_create3_det_click_batch_confirm')
          params.generateItems = [{ itemId: item.id, planId: item.planId }]
          params.batch = false
        }
        Lessons2.createGenerateLessonTasks(params).then(res => {
          this.$store.dispatch('unit/setBatchId', res.id)
        }).catch(() => {
          this.unGenerateLessons()
          this.$store.dispatch('unit/setAppearNotice', true)
        })
      }
    },

    // 新的批量生成课程
    batchNewGenerateUnitLessons(itemId) {
      // 关闭批量引导弹框
      localStorage.setItem(this.currentUser.user_id + 'SHOW_BATCH_GENERATE_LESSON_TIP', false)
      // 点击批量重置重试次数
      this.$store.dispatch('unit/setResetMaxRetry', 0)
      if (this.batchGenerateVisible) {
        this.hideGuide()
      }
      // 点击批量时先为 Vuex 中的 appearNotice 设置为 false
      this.$store.dispatch('unit/setAppearNotice', false)
      // 点击批量生成时先将 Vuex 中的数据清除
      this.$store.dispatch('unit/setBatchTasks', null)
      const result = this.unGenerateLessons()
      var items = [...new Set(result.items)]
      // items 中去除 itemId
      items = items.filter(item => !equalsIgnoreCase(item.itemId, itemId))
      // 判断 items 是否为空
      if (items.length === 0) {
        return
      }
      const params = {
        unitId: this.unit.id,
        generateItems: items,
        batch: true
      }
      Lessons2.createGenerateLessonTasks(params).then(res => {
        this.$store.dispatch('unit/setBatchId', res.id)
      }).catch(() => {
        this.unGenerateLessons()
        this.$store.dispatch('unit/setAppearNotice', true)
      })
    },

    // 未生成课程的活动项
    unGenerateLessons(processing = false) {
      let items = []
      let existUnConfirmed = false
      this.weeklyPlans.forEach(weeklyPlan => {
        weeklyPlan.items.forEach(item => {
          // 如果已经生成过了，无需再次生成
          if (item.lessonId) {
            return
          }
          if (!item.confirmed) {
            existUnConfirmed = true
          }
          // 如果当前的活动项存在，则该活动项的课程单个生成，不参与批量生成
          if (this.currentLessonItem) {
            if (equalsIgnoreCase(item.id, this.currentLessonItem.id)) {
              return
            }
          }
          if (processing) {
            this.$set(item, 'processing', true)
          } else {
            this.$set(item, 'processing', false)
          }
          const itemVo = {
            planId: item.planId,
            itemId: item.id
          }
          items.push(itemVo)
        })
        weeklyPlan.centerItems.forEach(item => {
          // 如果已经生成过了，无需再次生成
          if (item.lessonId) {
            return
          }
          if (!item.confirmed) {
            existUnConfirmed = true
          }
          // 如果当前的活动项存在，则该活动项的课程单个生成，不参与批量生成
          if (this.currentLessonItem) {
            if (equalsIgnoreCase(item.id, this.currentLessonItem.id)) {
              return
            }
          }
          if (processing) {
            this.$set(item, 'processing', true)
          } else {
            this.$set(item, 'processing', false)
          }
          const itemVo = {
            planId: item.planId,
            itemId: item.id
          }
          items.push(itemVo)
        })
      })
      return {
        items: items,
        existUnConfirmed: existUnConfirmed
      }
    }
  }
}
</script>

<style lang="less" scoped>
.rubric-option {
  .rubric-title {
    font-weight: bold;
    margin-bottom: 4px;
    white-space: normal;
    word-break: break-word;
  }

  .rubric-description {
    font-size: 12px;
    white-space: normal;
    word-break: break-word;
  }
}

/deep/ .update-lesson-rubrics-select {

  .el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
    top: calc(50% - 6px);
  }

  .el-select-dropdown {
    width: 560px;
  }
}

.custom-popper-class {
  width: 540px !important;
}

::v-deep {
  .update-lesson-measure-select {
    .el-select-dropdown {
      width: 560px;
    }
  }
}

.menu-item {
  position: relative;
  padding: 10px 14px;
  cursor: pointer;

  &:hover {
    background-color: #E7F7F8;
  }
}

.sub-menu-item {
  padding-left: 24px;
}

.active-menu {
  color: #10B3B7;
  background-color: #E7F7F8;
}

.adapted-tag-content {
  background: none !important;
  border: none !important;
  color: var(--ai-8-b-63-ff, #8B63FF);
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 16px;
  /* 133.333% */
}

.active-menu-mark {
  width: 3px;
  height: 100%;
  background-color: #10B3B7;
  position: absolute;
  left: 0;
  top: 0;
}

.lesson-confirm-status {
  flex-shrink: 0;
  flex-grow: 0;
}

/deep/ .el-collapse-item__header {
  line-height: unset !important;
  padding-left: 16px;
  padding-right: 4px;
  height: 60px;
}

/deep/ .active-week .el-collapse-item__header {
  background-color: #E7F7F8 !important;
}

/deep/ .el-collapse-item__content {
  padding-bottom: 0px !important;
}

/deep/ .el-collapse-item__arrow {
  font-size: 20px !important;
}

///deep/ .el-form-item__label {
//    margin-bottom: 0;
//    line-height: 22px;
//    font-weight: 600;
//}

/deep/ .confirm-generated-dialog {
  & .el-form-item__label {
    margin-bottom: 0;
    line-height: 22px;
    font-weight: 600;
  }

  & .el-dialog__body {
    padding: 0 20px;
  }

  & .el-tag.el-tag--info {
    color: #676879;
  }
}

/deep/ .batch-generated-dialog {

  width: 600px;

  & .el-dialog__body {
    padding: 10px 20px;
  }

  & .el-tag.el-tag--info {
    color: #676879;
  }
}
</style>
