<template>
  <el-popover
    placement="left-start"
    trigger="manual"
    v-model="showLessonModuleSettingGuide"
    popper-class="lesson-module-setting-guide"
    width="450"
  >
  <div class="text-white">
    <!-- 引导文字 -->
    <div class="lg-margin-bottom-24 word-break text-left">
        <!-- 用户引导内容 -->
        <span class="title-font-20">Customize lesson plan modules</span>
        <div class="title-font-14">You can customize the required lesson modules, accurately generate the lesson content you need for all units.</div>
        <div class="new-feature">
          <span>Customize lesson plan modules</span>
          <span class="newPoint point-relative">{{ $t('loc.new') }}</span>
        </div>
    </div>
    <div class="display-flex flex-justify-end gap-6 align-items">
        <el-button type="text" @click="hideGuide(false)">{{ $t('loc.unitPlannerRedesignGuide2') }}</el-button>
        <el-button type="primary" @click="hideGuide(true)">Apply Now</el-button>
    </div>
  </div>
    <!-- 课程模块设置 -->
    <el-link slot="reference" class="settings-style"
            :underline="false">
            <LessonModuleSettings ref="lessonModuleSettings">
              <span slot="content">
                Customize lesson plan modules
                <span class="newPoint point-relative">{{ $t('loc.new') }}</span>
              </span>
            </LessonModuleSettings>
    </el-link>
  </el-popover>
</template>
<script>
import LessonModuleSettings from './LessonModuleSettings.vue'
import { mapState } from 'vuex'
export default {
  name: 'LessonModuleSettingTips',
  components: {
    LessonModuleSettings
  },
  computed: {
    ...mapState({
      guideFeatures: state => state.common.guideFeatures, // 功能引导
    })
  },
  data () {
    return {
      showLessonModuleSettingGuide: false // 是否展示引导
    }
  },
  methods: {
    // 展示引导
    showGuide () {
      if (this.guideFeatures && this.guideFeatures.showLessonModuleSettingGuide) {
        this.showLessonModuleSettingGuide = true
        let params = { features: ['LESSON_MODULE_SETTING_GUIDE'] }
        this.$axios.post($api.urls().hideGuide, params)
        // 更新引导状态
        this.$store.commit('SET_USER_NEED_GUIDE_FEATURES', {
          ...this.guideFeatures,
          showLessonModuleSettingGuide: false
        })
      }
    },
    // 关闭引导
    hideGuide (setNow) {
      this.showLessonModuleSettingGuide = false
      if (setNow) {
        this.$refs.lessonModuleSettings.openDialog()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.settings-style {
  padding: 8px 16px;
  line-height: 24px;
}
</style>
<style lang="scss">
.el-popper.lesson-module-setting-guide {
  background: var(--color-ai-assistant);
  color: #ffffff;
  padding: 24px;
  border: none;

  &.el-popper[x-placement^='left'] .popper__arrow::after {
    border-left-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^='right'] .popper__arrow::after {
    border-right-color: var(--color-ai-assistant);
  }

  &.el-popper[x-placement^='bottom'] .popper__arrow {
    display: block !important;
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^='bottom'] .popper__arrow::after {
    border-bottom-color: var(--color-ai-assistant) !important;
  }

  &.el-popper[x-placement^='top'] .popper__arrow::after {
    border-top-color: var(--color-ai-assistant);
  }

  .new-feature {
    color: var(--color-primary);
    font-size: 16px;
    line-height: 26px;
    padding: 12px;
    margin-top: 16px;
    background: var(--ffffff, #fff);
    border-radius: 8px;
  }

  .el-button--text {
    color: var(--ffffff, #fff) !important;
    border: none !important;
    background: transparent !important;
  }

  .el-button {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #dcdfe6);
    background: var(--ffffff, #fff);
    color: var(--color-ai-assistant);
  }

  .el-button:hover {
    border-radius: 4px;
    border: 2px solid var(--dcdfe-6, #dcdfe6);
    background: var(--ffffff, #fff);
  }
}
</style>